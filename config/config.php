<?php
/**
 * 京东联盟服务配置文件
 * 基于大淘客项目架构设计
 */

return [
    // 调试模式
    'debug' => true,
    
    // 应用信息
    'app' => [
        'name' => '京东联盟服务端',
        'version' => '1.0.0',
        'timezone' => 'Asia/Shanghai',
    ],
    
    // 数据库配置
    'database' => [
        'host' => '127.0.0.1',
        'port' => 3306,
        'database' => 'jd_union_service',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => 'jd_',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ],
    ],
    
    // 京东联盟API配置
    'jd_union' => [
        'name' => '京东联盟',
        'enabled' => true,
        'app_key' => '', // 京东联盟App Key
        'app_secret' => '', // 京东联盟App Secret
        'site_id' => '', // 网站ID/推广位ID
        'position_id' => '', // 推广位ID
        'union_id' => '', // 联盟ID
        'api_url' => 'https://api.jd.com/routerjson',
        'version' => '1.0',
        'format' => 'json',
        'timeout' => 30,
        'retry_times' => 3,
        'rate_limit' => 100, // 每分钟请求限制
    ],
    
    // 缓存配置
    'cache' => [
        'enabled' => true,
        'driver' => 'file', // file, redis, memcached
        'path' => __DIR__ . '/../cache/',
        'prefix' => 'jd_union_',
        'ttl' => [
            'goods_list' => 3600, // 商品列表缓存1小时
            'goods_detail' => 7200, // 商品详情缓存2小时
            'promotion_link' => 1800, // 推广链接缓存30分钟
            'category' => 86400, // 分类缓存24小时
        ],
        // Redis配置（如果使用Redis）
        'redis' => [
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'database' => 0,
        ],
    ],
    
    // 日志配置
    'log' => [
        'enabled' => true,
        'level' => 'info', // debug, info, warning, error
        'path' => __DIR__ . '/../logs/',
        'filename' => 'app-{date}.log',
        'max_files' => 30, // 保留30天的日志
        'format' => '[{datetime}] {level}: {message} {context}',
    ],
    
    // API接口配置
    'api' => [
        'enabled' => true,
        'prefix' => '/api',
        'version' => 'v1',
        'rate_limit' => [
            'enabled' => true,
            'requests' => 1000, // 每小时请求数
            'window' => 3600, // 时间窗口（秒）
        ],
        'cors' => [
            'enabled' => true,
            'origins' => ['*'],
            'methods' => ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            'headers' => ['Content-Type', 'Authorization', 'X-Requested-With'],
        ],
    ],
    
    // 管理后台配置
    'admin' => [
        'enabled' => true,
        'path' => '/admin',
        'username' => 'admin',
        'password' => 'admin123', // 请修改默认密码
        'session_timeout' => 7200, // 2小时
        'theme' => 'default',
    ],
    
    // 数据同步配置
    'sync' => [
        'enabled' => true,
        'auto_sync' => false, // 是否自动同步
        'sync_interval' => 3600, // 同步间隔（秒）
        'batch_size' => 100, // 每批处理数量
        'max_pages' => 10, // 最大页数
        'categories' => [
            1, // 好券商品
            2, // 精选卖场
            10, // 9.9包邮
            15, // 京东配送
            22, // 实时热销榜
            23, // 为你推荐
        ],
    ],
    
    // 商品过滤配置
    'goods_filter' => [
        'min_price' => 0, // 最低价格
        'max_price' => 0, // 最高价格（0表示不限制）
        'min_commission_rate' => 0, // 最低佣金比例
        'has_coupon' => false, // 是否必须有优惠券
        'is_jd_sale' => false, // 是否京东自营
        'exclude_keywords' => [], // 排除关键词
        'include_keywords' => [], // 包含关键词
    ],
    
    // 错误处理配置
    'error' => [
        'display_errors' => true,
        'log_errors' => true,
        'error_reporting' => E_ALL,
        'exception_handler' => true,
    ],
    
    // 安全配置
    'security' => [
        'csrf_protection' => true,
        'xss_protection' => true,
        'sql_injection_protection' => true,
        'rate_limiting' => true,
        'ip_whitelist' => [], // IP白名单
        'ip_blacklist' => [], // IP黑名单
    ],
    
    // 性能配置
    'performance' => [
        'gzip_compression' => true,
        'browser_cache' => true,
        'cache_headers' => true,
        'minify_output' => false,
    ],
    
    // 通知配置
    'notification' => [
        'enabled' => false,
        'email' => [
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_username' => '',
            'smtp_password' => '',
            'from_email' => '',
            'from_name' => '京东联盟服务',
        ],
        'webhook' => [
            'url' => '',
            'secret' => '',
        ],
    ],
    
    // 监控配置
    'monitor' => [
        'enabled' => true,
        'check_interval' => 300, // 5分钟
        'alert_threshold' => [
            'error_rate' => 0.05, // 错误率5%
            'response_time' => 5000, // 响应时间5秒
            'memory_usage' => 0.8, // 内存使用率80%
        ],
    ],
];
?>
