# 好单库API集成文档

## 📋 概述

好单库是一个专业的淘客选品平台，提供丰富的商品数据和优惠券信息。本项目已集成好单库API，支持商品列表、商品详情、商品搜索、分类列表等功能。

## 🔧 配置

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 好单库API配置
HAODANKU_ENABLED=true
HAODANKU_API_KEY=your_haodanku_api_key_here
HAODANKU_BASE_URL=http://v2.api.haodanku.com
```

### 2. 获取API Key

1. 访问 [好单库开放平台](https://www.haodanku.com/openapi)
2. 注册并登录账号
3. 创建应用获取API Key
4. 将API Key配置到环境变量中

## 🚀 API接口

### 1. 商品列表

**接口地址**: `GET /api/haodanku/goods`

**请求参数**:
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20，最大100
- `sort` (string): 排序方式，可选值：
  - `sale_num`: 按销量排序（默认）
  - `price`: 按价格排序
  - `coupon_amount`: 按优惠券金额排序
  - `commission_rate`: 按佣金比例排序
- `cid` (string): 分类ID，可选
- `subcid` (string): 子分类ID，可选
- `back` (string): 返利比例筛选，可选
- `min_id` (string): 最小ID，用于翻页，可选

**请求示例**:
```bash
GET /api/haodanku/goods?page=1&page_size=20&sort=sale_num
```

**响应示例**:
```json
{
    "code": 200,
    "message": "获取商品列表成功",
    "data": {
        "list": [
            {
                "id": "123456",
                "itemtitle": "商品标题",
                "itemprice": "99.00",
                "itemsale": "1000",
                "coupon_amount": "10",
                "commission_rate": "15.5"
            }
        ],
        "pagination": {
            "current_page": 1,
            "page_size": 20,
            "total_count": 1000,
            "total_pages": 50
        }
    }
}
```

### 2. 商品详情

**接口地址**: `GET /api/haodanku/detail`

**请求参数**:
- `item_id` (string): 商品ID，必填
- `id` (string): 商品ID的别名，可选

**请求示例**:
```bash
GET /api/haodanku/detail?item_id=123456
```

### 3. 商品搜索

**接口地址**: `GET /api/haodanku/search`

**请求参数**:
- `keyword` (string): 搜索关键词，必填，2-50个字符
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20，最大100
- `sort` (string): 排序方式，同商品列表
- `cid` (string): 分类ID，可选
- `subcid` (string): 子分类ID，可选
- `back` (string): 返利比例筛选，可选

**请求示例**:
```bash
GET /api/haodanku/search?keyword=手机&page=1&page_size=10
```

### 4. 分类列表

**接口地址**: `GET /api/haodanku/categories`

**请求参数**: 无

**请求示例**:
```bash
GET /api/haodanku/categories
```

### 5. 超值买返商品

**接口地址**: `GET /api/haodanku/supervalue`

**请求参数**:
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20，最大100
- `cid` (string): 分类ID，可选
- `subcid` (string): 子分类ID，可选

**请求示例**:
```bash
GET /api/haodanku/supervalue?page=1&page_size=20
```

## 🧪 测试

### 运行测试脚本

```bash
# 命令行测试
php test_haodanku.php

# 或者在浏览器中访问
http://localhost:81/test_haodanku.php
```

测试脚本会验证：
- 好单库服务初始化
- 获取商品列表
- 搜索商品功能
- 获取分类列表

## 📝 使用示例

### PHP代码示例

```php
<?php
use Services\HaodankuService;

// 初始化服务
$haodankuService = new HaodankuService();

// 获取商品列表
$goodsList = $haodankuService->getGoodsList([
    'page' => 1,
    'page_size' => 20,
    'sort' => 'sale_num'
]);

// 搜索商品
$searchResult = $haodankuService->searchGoods('手机', [
    'page' => 1,
    'page_size' => 10
]);

// 获取商品详情
$goodsDetail = $haodankuService->getGoodsDetail('123456');

// 获取分类列表
$categories = $haodankuService->getCategories();
?>
```

### JavaScript调用示例

```javascript
// 获取商品列表
fetch('/api/haodanku/goods?page=1&page_size=20&sort=sale_num')
    .then(response => response.json())
    .then(data => {
        console.log('商品列表:', data);
    });

// 搜索商品
fetch('/api/haodanku/search?keyword=手机&page=1&page_size=10')
    .then(response => response.json())
    .then(data => {
        console.log('搜索结果:', data);
    });

// 获取商品详情
fetch('/api/haodanku/detail?item_id=123456')
    .then(response => response.json())
    .then(data => {
        console.log('商品详情:', data);
    });
```

## 🔍 错误处理

所有API接口都包含完善的错误处理：

```json
{
    "code": 400,
    "message": "请提供搜索关键词",
    "data": null
}
```

常见错误码：
- `400`: 请求参数错误
- `500`: 服务器内部错误
- `200`: 请求成功

## 📊 缓存机制

- **商品列表**: 缓存5分钟
- **商品详情**: 缓存10分钟
- **搜索结果**: 缓存5分钟
- **分类列表**: 缓存1小时

## 🔧 高级配置

### 自定义API地址

如果需要使用其他API地址，可以在环境变量中配置：

```env
HAODANKU_BASE_URL=https://your-custom-api-url.com
```

### 禁用好单库功能

```env
HAODANKU_ENABLED=false
```

## 📚 相关文档

- [好单库官方文档](https://www.haodanku.com/openapi)
- [API接口说明](https://www.haodanku.com/openapi/api_detail)
- [开发者中心](https://www.haodanku.com/openapi/api_apply)

## 🤝 技术支持

如果在使用过程中遇到问题，可以：

1. 查看日志文件：`logs/app.log`
2. 运行测试脚本检查配置
3. 检查API Key是否正确配置
4. 确认网络连接正常

## 📄 许可证

本项目遵循 MIT 许可证。
