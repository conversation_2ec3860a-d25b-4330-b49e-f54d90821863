# 京东联盟API快速参考

## 🚀 快速开始

### 1. 基础配置
```php
$config = [
    'app_key' => 'your_app_key',
    'app_secret' => 'your_app_secret',
    'use_official_format' => true  // 必须启用
];
$client = new JdClient($config);
```

### 2. 商品排行榜 (推荐使用)
```php
// 获取全部商品24小时排行榜
$req = new UnionOpenGoodsRankQueryRequest();
$rankGoodsReq = new \UnionOpenGoodsRankQuery\RankGoodsReq();
$rankGoodsReq->setRankId(200000);  // 全部商品
$rankGoodsReq->setSortType(3);     // 24小时排序
$req->setRankGoodsReq($rankGoodsReq->getInstance());
$response = $client->execute($req);
```

### 3. 精选商品查询 (推荐使用)
```php
// 获取好券商品
$req = new UnionOpenGoodsJingfenQueryRequest();
$goodsReq = new \UnionOpenGoodsJingfenQuery\GoodsReq();
$goodsReq->setEliteId(1);          // 好券商品
$goodsReq->setPageIndex(1);
$goodsReq->setPageSize(20);
// 重要：不要设置pid参数
$req->setGoodsReq($goodsReq->getInstance());
$response = $client->execute($req);
```

### 4. 商品物料查询 (推荐使用)
```php
// 获取猜你喜欢商品
$req = new UnionOpenGoodsMaterialQueryRequest();
$goodsReq = new \UnionOpenGoodsMaterialQuery\GoodsReq();
$goodsReq->setEliteId(1);          // 猜你喜欢
$goodsReq->setPageIndex(1);
$goodsReq->setPageSize(20);
// 重要：不要设置pid参数
$req->setGoodsReq($goodsReq->getInstance());
$response = $client->execute($req);
```

### 5. 活动查询 (推荐使用)
```php
// 获取活动列表
$req = new UnionOpenActivityQueryRequest();
$activityReq = new \UnionOpenActivityQuery\ActivityReq();
$activityReq->setPageIndex(1);
$activityReq->setPageSize(20);
$req->setActivityReq($activityReq->getInstance());
$response = $client->execute($req);
```

## 📊 参数速查表

### 排行榜类型 (rankId)
| ID | 类型 | ID | 类型 |
|----|------|----|------|
| 200000 | 全部 | 200001 | 食品酒水 |
| 200002 | 家庭清洁 | 200003 | 个护美妆 |
| 200004 | 医药保健 | 200005 | 生鲜 |
| 200006 | 数码家电 | 200007 | 家居日用 |
| 200008 | 时尚生活 | | |

### 排序类型 (sortType)
| ID | 类型 | 说明 |
|----|------|------|
| 1 | 2小时 | 2小时内热销 |
| 2 | 高佣 | 按佣金比例排序 |
| 3 | 24小时 | 24小时内热销 |

### 精选池类型 (eliteId)
| ID | 类型 | 说明 |
|----|------|------|
| 1 | 好券商品 | 有优惠券的商品 |
| 2 | 精选卖场 | 精选商品 |
| 10 | 9.9包邮 | 9.9元包邮商品 |
| 22 | 京东爆品 | 热门爆款商品 |

### 物料频道类型 (eliteId)
| ID | 频道名称 | 状态 |
|----|----------|------|
| 1 | 猜你喜欢 | ✅ 可用 |
| 2 | 实时热销 | ❌ 需要PID |
| 3 | 大额券 | ❌ 需要PID |
| 4 | 9.9包邮 | ❌ 需要PID |
| 13270 | 国补商品 | ❌ 需要PID |

## 🔧 常用代码片段

### 获取数码家电高佣排行榜
```php
$rankGoodsReq->setRankId(200006);  // 数码家电
$rankGoodsReq->setSortType(2);     // 高佣排序
```

### 获取9.9包邮商品
```php
$goodsReq->setEliteId(10);         // 9.9包邮
$goodsReq->setPageSize(50);        // 获取更多商品
```

### 获取商品物料数据
```php
$goodsReq->setEliteId(1);          // 猜你喜欢
$goodsReq->setPageSize(20);        // 每页20个
// 不设置pid参数
```

### 获取活动数据
```php
$activityReq->setPageSize(50);     // 获取更多活动
$activityReq->setPoolId(1);        // 指定活动池（可选）
```

### 批量获取多个榜单
```php
$rankIds = [200001, 200003, 200006]; // 食品、美妆、数码
foreach ($rankIds as $rankId) {
    $rankGoodsReq->setRankId($rankId);
    $response = $client->execute($req);
    // 处理每个榜单的数据
}
```

## 📋 返回数据关键字段

### 商品基本信息
```php
$item['skuName']           // 商品名称
$item['itemId']            // 联盟商品ID ⭐
$item['materialUrl']       // 推广链接
```

### 价格信息
```php
$item['priceInfo']['price']              // 原价
$item['priceInfo']['lowestPrice']        // 最低价
$item['priceInfo']['lowestCouponPrice']  // 券后价
```

### 佣金信息
```php
$item['commissionInfo']['commission']      // 佣金金额
$item['commissionInfo']['commissionShare'] // 佣金比例
```

### 优惠券信息
```php
$item['couponInfo']['couponList'][0]['discount'] // 优惠券面额
$item['couponInfo']['couponList'][0]['quota']    // 使用门槛
$item['couponInfo']['couponList'][0]['link']     // 优惠券链接
```

### 推广链接信息
```php
// 从精选商品API获取
$item['materialUrl']                             // 推广链接

// 从物料查询API获取
$item['promotionInfo']['clickURL']               // 推广链接
```

### 活动信息
```php
$activity['title']                               // 活动名称
$activity['advantage']                           // 活动优势
$activity['startTime']                           // 开始时间（毫秒时间戳）
$activity['endTime']                             // 结束时间（毫秒时间戳）
$activity['urlM']                                // 移动端链接
$activity['urlPC']                               // PC端链接
$activity['imgList'][0]['imgUrl']                // 活动图片
```

## ⚠️ 重要提示

### ✅ 推荐做法
- 使用 `use_official_format => true`
- 精选商品查询和物料查询都不设置pid参数
- 实现适当的缓存机制
- 添加错误重试逻辑
- 优先使用已开通的API获取推广链接

### ❌ 避免的问题
- 不要使用旧版本的参数格式
- 不要在精选商品查询和物料查询中设置pid
- 不要过于频繁调用API
- 不要忽略错误处理
- 不要直接调用需要权限申请的推广API

## 🔍 调试技巧

### 查看原始响应
```php
// JdClient会自动输出调试信息
echo "原始响应: " . $response . "\n";
```

### 检查权限状态
```php
try {
    $response = $client->execute($req);
} catch (Exception $e) {
    if (strpos($e->getMessage(), '403') !== false) {
        echo "权限不足，需要申请API权限\n";
    }
}
```

### 验证数据完整性
```php
if (isset($response['data']) && !empty($response['data'])) {
    echo "成功获取 " . count($response['data']) . " 个商品\n";
} else {
    echo "未获取到商品数据，检查参数设置\n";
}
```

## 📞 技术支持

### 权限申请
- 联系京东联盟业务团队
- 说明具体的API使用需求
- 提供技术方案和预期调用量

### 常见问题
1. **403错误**：权限不足，需要申请API权限
2. **400错误**：参数格式错误，检查必填参数
3. **空数据**：可能是参数设置问题，特别是pid参数
4. **2001701错误**：siteId类型不支持，需要网站/APP类型的推广位
5. **参数类型错误**：subUnionId需要字符串类型，不是整数

---

📝 **文档版本**：v2.0 (2025-07-17)
🔄 **最后更新**：2025年7月17日
📋 **新增内容**：商品物料查询、活动查询、推广API权限状态
