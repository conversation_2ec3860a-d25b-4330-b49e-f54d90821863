# 京东联盟API接口使用指南

## 概述

本文档记录了京东联盟API接口的使用方法，包括已测试成功的接口和配置要求。

> 📋 **项目状态**: 如需了解项目整体状态和继任者指南，请查看 [项目状态报告](project-status.md)

## 配置要求

### 1. 环境配置

确保 `.env` 文件中包含以下配置：

```env
# 京东联盟配置
JD_UNION_APP_KEY=your_app_key
JD_UNION_APP_SECRET=your_app_secret
JD_UNION_SITE_ID=your_site_id
JD_UNION_POSITION_ID=your_position_id
JD_UNION_UNION_ID=your_union_id
```

### 2. JdClient配置

使用官方标准格式（推荐）：

```php
$config = [
    'app_key' => 'your_app_key',
    'app_secret' => 'your_app_secret',
    'position_id' => 'your_position_id',
    'union_id' => 'your_union_id',
    'use_official_format' => true  // 启用官方格式
];

$client = new JdClient($config);
```

## 已测试的API接口

### ✅ 已开通且可用的API

#### 1. 商品排行榜查询 (jd.union.open.goods.rank.query)

#### 接口说明
获取京东联盟商品排行榜数据，支持多种榜单类型和排序方式。

#### 权限状态
✅ **已开通** - 可以正常调用并返回数据

#### 使用方法

```php
// 加载请求类
require_once 'jos-php-open-api-sdk-2.0/jd/request/UnionOpenGoodsRankQueryRequest.php';
require_once 'jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenGoodsRankQuery/RankGoodsReq.php';

// 创建请求对象
$req = new UnionOpenGoodsRankQueryRequest();

// 创建参数对象
$rankGoodsReq = new \UnionOpenGoodsRankQuery\RankGoodsReq();
$rankGoodsReq->setRankId(200000);    // 榜单ID
$rankGoodsReq->setSortType(3);       // 排序类型
$rankGoodsReq->setPageIndex(1);      // 页码（可选）
$rankGoodsReq->setPageSize(10);      // 每页数量（可选）

// 设置请求参数
$req->setRankGoodsReq($rankGoodsReq->getInstance());
$req->setVersion("1.0");

// 执行请求
$response = $client->execute($req);
```

#### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| rankId | Number | 是 | 榜单ID |
| sortType | Number | 是 | 排序类型 |
| pageIndex | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认10 |

#### rankId 榜单类型

| rankId | 榜单名称 |
|--------|----------|
| 200000 | 全部 |
| 200001 | 食品酒水 |
| 200002 | 家庭清洁 |
| 200003 | 个护美妆 |
| 200004 | 医药保健 |
| 200005 | 生鲜 |
| 200006 | 数码家电 |
| 200007 | 家居日用 |
| 200008 | 时尚生活 |

#### sortType 排序类型

| sortType | 排序方式 |
|----------|----------|
| 1 | 2小时 |
| 2 | 高佣 |
| 3 | 24小时 |

#### 返回数据结构

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "skuName": "商品名称",
      "itemId": "联盟商品ID",
      "purchasePriceInfo": {
        "purchasePrice": 44.90
      },
      "commission": 1.84,
      "commissionShare": 4.1,
      "imageUrl": "商品主图",
      "comments": 5000000,
      "goodComments": 5000000,
      "goodCommentsShare": 99
    }
  ],
  "totalCount": 100
}
```

#### 2. 精选商品查询 (jd.union.open.goods.jingfen.query)

#### 接口说明
获取京东联盟精选商品数据，包括好券商品、9.9包邮等精选池。

#### 权限状态
✅ **已开通** - 可以正常调用并返回数据

#### 使用方法

```php
// 加载请求类
require_once 'jos-php-open-api-sdk-2.0/jd/request/UnionOpenGoodsJingfenQueryRequest.php';
require_once 'jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenGoodsJingfenQuery/GoodsReq.php';

// 创建请求对象
$req = new UnionOpenGoodsJingfenQueryRequest();

// 创建参数对象
$goodsReq = new \UnionOpenGoodsJingfenQuery\GoodsReq();
$goodsReq->setEliteId(1);           // 精选池ID
$goodsReq->setPageIndex(1);         // 页码
$goodsReq->setPageSize(20);         // 每页数量
// 注意：不要设置pid参数，会导致返回空数据

// 设置请求参数
$req->setGoodsReq($goodsReq->getInstance());
$req->setVersion("1.0");

// 执行请求
$response = $client->execute($req);
```

#### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| eliteId | Number | 是 | 精选池ID |
| pageIndex | Number | 是 | 页码 |
| pageSize | Number | 是 | 每页数量 |
| pid | String | 否 | 推广位ID（建议不设置） |

#### eliteId 精选池类型

| eliteId | 精选池名称 |
|---------|------------|
| 1 | 好券商品 |
| 2 | 精选卖场 |
| 10 | 9.9包邮 |
| 22 | 京东爆品 |

#### ⚠️ 重要提示

**不要设置pid参数**：测试发现，当设置 `pid` 参数时，API会返回空数据。建议不设置此参数以获得最佳结果。

#### 3. 商品物料查询 (jd.union.open.goods.material.query)

##### 接口说明
获取京东联盟商品物料数据，支持多种频道类型，包含丰富的商品信息和推广链接。

##### 权限状态
✅ **已开通** - 可以正常调用并返回数据

##### 使用方法

```php
// 加载请求类
require_once 'jos-php-open-api-sdk-2.0/jd/request/UnionOpenGoodsMaterialQueryRequest.php';
require_once 'jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenGoodsMaterialQuery/GoodsReq.php';

// 创建请求对象
$req = new UnionOpenGoodsMaterialQueryRequest();

// 创建参数对象
$goodsReq = new \UnionOpenGoodsMaterialQuery\GoodsReq();
$goodsReq->setEliteId(1);           // 频道ID
$goodsReq->setPageIndex(1);         // 页码
$goodsReq->setPageSize(20);         // 每页数量
// 重要：不要设置pid参数

// 设置请求参数
$req->setGoodsReq($goodsReq->getInstance());
$req->setVersion("1.0");

// 执行请求
$response = $client->execute($req);
```

##### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| eliteId | Number | 是 | 频道ID |
| pageIndex | Number | 是 | 页码 |
| pageSize | Number | 是 | 每页数量 |
| pid | String | 否 | 推广位ID（建议不设置） |

##### eliteId 频道类型

| eliteId | 频道名称 | 状态 |
|---------|----------|------|
| 1 | 猜你喜欢 | ✅ 可用 |
| 2 | 实时热销 | ❌ 需要PID |
| 3 | 大额券 | ❌ 需要PID |
| 4 | 9.9包邮 | ❌ 需要PID |
| 13270 | 国补商品 | ❌ 需要PID |

##### ⚠️ 重要提示

**不要设置pid参数**：与精选商品查询API类似，当设置 `pid` 参数时，API会返回"已达到最后一页"错误。建议不设置此参数。

##### 返回数据结构

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "skuName": "商品名称",
      "itemId": "联盟商品ID",
      "priceInfo": {
        "price": 369.0,
        "lowestPrice": 369.0,
        "lowestCouponPrice": 369.0
      },
      "commissionInfo": {
        "commission": 11.07,
        "commissionShare": 3.0
      },
      "resourceInfo": {
        "eliteId": 1,
        "eliteName": "猜你喜欢【迁移】"
      },
      "promotionInfo": {
        "clickURL": "https://union-click.jd.com/jdc?e=..."
      },
      "imageInfo": {
        "imageList": [
          {"url": "商品图片链接"}
        ]
      },
      "videoInfo": {
        "videoList": [
          {"url": "商品视频链接"}
        ]
      }
    }
  ]
}
```

#### 4. 活动查询 (jd.union.open.activity.query)

##### 接口说明
获取京东联盟活动数据，包括国补活动、品类活动、促销活动等。

##### 权限状态
✅ **已开通** - 可以正常调用并返回数据

##### 使用方法

```php
// 加载请求类
require_once 'jos-php-open-api-sdk-2.0/jd/request/UnionOpenActivityQueryRequest.php';
require_once 'jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenActivityQuery/ActivityReq.php';

// 创建请求对象
$req = new UnionOpenActivityQueryRequest();

// 创建参数对象
$activityReq = new \UnionOpenActivityQuery\ActivityReq();
$activityReq->setPageIndex(1);      // 页码
$activityReq->setPageSize(20);      // 每页数量
// 可选参数
$activityReq->setPoolId(1);         // 活动池ID（可选）
$activityReq->setActivityId(123);   // 特定活动ID（可选）

// 设置请求参数
$req->setActivityReq($activityReq->getInstance());
$req->setVersion("1.0");

// 执行请求
$response = $client->execute($req);
```

##### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| pageIndex | Number | 是 | 页码 |
| pageSize | Number | 是 | 每页数量 |
| poolId | Number | 否 | 活动池ID |
| activityId | Number | 否 | 特定活动ID |

##### 返回数据结构

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 283077,
      "title": "超级国补日",
      "advantage": "国补双重补贴立减50%",
      "actStatus": 3,
      "startTime": 1741536000000,
      "endTime": 1742140800000,
      "urlM": "https://pro.m.jd.com/mall/active/...",
      "urlPC": "https://pro.jd.com/mall/active/...",
      "categoryList": [
        {"categoryId": 6196, "type": 1}
      ],
      "imgList": [
        {
          "imgName": "630x260.png",
          "imgUrl": "https://img14.360buyimg.com/...",
          "widthHeight": "630*260"
        }
      ]
    }
  ],
  "totalCount": 23348
}
```

##### 活动类型示例

- **国补活动**：超级国补日、3C数码国补日
- **品类活动**：低温乳品品类日、京东超市水饮节
- **促销活动**：双11、心动购物季
- **季节活动**：暑期清凉季、黑色星期五

### ❌ 需要申请权限的API

#### 1. 推广链接生成API

##### 1.1 通过UnionID生成推广链接 (jd.union.open.promotion.byunionid.get)

**权限状态**: ❌ **403权限错误** - 需要申请API权限

**错误信息**: `无访问权限`

**使用方法**（权限申请后）:
```php
require_once 'jos-php-open-api-sdk-2.0/jd/request/UnionOpenPromotionByunionidGetRequest.php';
require_once 'jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenPromotionByunionidGet/PromotionCodeReq.php';

$req = new UnionOpenPromotionByunionidGetRequest();
$promotionReq = new \UnionOpenPromotionByunionidGet\PromotionCodeReq();

$promotionReq->setMaterialId('https://item.jd.com/100012043978.html');
$promotionReq->setUnionId(intval($config['union_id']));
$promotionReq->setPositionId(intval($config['position_id']));

$req->setPromotionCodeReq($promotionReq->getInstance());
$response = $client->execute($req);
```

##### 1.2 通过SubUnionID生成推广链接 (jd.union.open.promotion.bysubunionid.get)

**权限状态**: ❌ **403权限错误** - 需要申请API权限

**错误信息**: `无访问权限`

**使用方法**（权限申请后）:
```php
require_once 'jos-php-open-api-sdk-2.0/jd/request/UnionOpenPromotionBysubunionidGetRequest.php';
require_once 'jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenPromotionBysubunionidGet/PromotionCodeReq.php';

$req = new UnionOpenPromotionBysubunionidGetRequest();
$promotionReq = new \UnionOpenPromotionBysubunionidGet\PromotionCodeReq();

$promotionReq->setMaterialId('https://item.jd.com/100012043978.html');
$promotionReq->setSubUnionId(strval($config['union_id'])); // 注意：需要字符串类型
$promotionReq->setPositionId(intval($config['position_id']));

$req->setPromotionCodeReq($promotionReq->getInstance());
$response = $client->execute($req);
```

##### 1.3 通用推广链接生成 (jd.union.open.promotion.common.get)

**权限状态**: ❌ **参数配置错误** - 需要网站/APP类型的siteId

**错误信息**: `不支持siteId用于此种方式推广,只支持网站/APP`

**问题分析**: 当前的siteId可能不是网站/APP类型，需要检查推广位配置

##### 1.4 小程序推广链接 (jd.union.open.promotion.applet.get)

**权限状态**: ❌ **403权限错误** - 需要申请API权限

**错误信息**: `无访问权限`

#### 2. 商品大字段查询 (jd.union.open.goods.bigfield.query)

**权限状态**: ❌ **参数类型错误** - 需要修复SDK参数类型

**错误信息**: `skuIds参数期望数组类型，但传递的是字符串`

**问题分析**: SDK中的参数设置方法需要调整，应该传递数组而不是字符串

### 🔧 技术修复状态

#### ✅ 已修复的问题

1. **subUnionId参数类型问题** - 已修复为字符串类型
2. **API调用格式** - 所有API调用格式已正确配置
3. **错误处理机制** - 完善的错误处理和调试信息

#### 🔄 待修复的问题

1. **商品大字段查询API** - 需要修复skuIds参数类型
2. **通用推广API** - 需要检查siteId配置类型

### 🎯 当前可用的替代方案

#### 获取推广链接的替代方法

虽然推广链接生成API需要权限申请，但可以从已开通的API中获取推广链接：

```php
// 从精选商品查询API获取推广链接
$response = $client->execute($jingfenRequest);
foreach ($response['data'] as $item) {
    $promotionUrl = $item['materialUrl']; // 这就是推广链接
    $itemId = $item['itemId'];           // 联盟商品ID
}

// 从商品物料查询API获取推广链接
$response = $client->execute($materialRequest);
foreach ($response['data'] as $item) {
    $promotionUrl = $item['promotionInfo']['clickURL']; // 推广链接
}
```

#### 返回数据结构

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "skuName": "商品名称",
      "itemId": "联盟商品ID",
      "materialUrl": "推广链接",
      "priceInfo": {
        "price": 7.99,
        "lowestPrice": 7.99,
        "lowestCouponPrice": 5.99
      },
      "commissionInfo": {
        "commission": 2.4,
        "commissionShare": 30.0
      },
      "couponInfo": {
        "couponList": [
          {
            "discount": 2.0,
            "quota": 2.01,
            "link": "优惠券链接"
          }
        ]
      },
      "imageInfo": {
        "imageList": [
          {"url": "商品图片链接"}
        ]
      },
      "shopInfo": {
        "shopName": "店铺名称",
        "shopLevel": 4.0
      }
    }
  ],
  "totalCount": 3758
}
```

## 联盟商品ID (itemId) 格式

### 格式特征
- **长度**：约43-47个字符
- **结构**：`{Base64编码部分}_{推广位标识}`
- **示例**：`YSrRfxC3pVJzQ8y1An7dbEap_391fhqZjnp1Esp7ePK`

### 用途
1. **推广链接生成**：用于生成联盟推广链接
2. **佣金追踪**：包含推广位信息，用于佣金结算
3. **商品标识**：联盟系统中的唯一商品标识

## 错误处理

### 常见错误及解决方案

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 403 | 无访问权限 | 联系京东联盟申请API权限 |
| 400 | 参数错误 | 检查参数格式和必填项 |
| 15 | 不存在的方法名 | 确认API方法名正确 |

### 调试建议

1. **启用调试模式**：在JdClient中会输出详细的请求和响应信息
2. **检查配置**：确认APP Key、Secret等配置正确
3. **参数验证**：确保所有必填参数都已设置
4. **权限确认**：在京东联盟后台确认API权限状态

## 最佳实践

1. **使用官方格式**：设置 `use_official_format => true`
2. **错误重试**：实现适当的重试机制
3. **缓存策略**：对商品数据进行合理缓存
4. **限流控制**：避免过于频繁的API调用
5. **日志记录**：记录API调用日志便于问题排查

## 示例代码

### 完整示例：获取好券商品

```php
<?php
require_once 'src/Utils/EnvLoader.php';
require_once 'src/Services/JdClient.php';

use Utils\EnvLoader;
use Services\JdClient;

// 加载配置
EnvLoader::load();
$config = EnvLoader::getJdUnionConfig();
$config['use_official_format'] = true;

// 创建客户端
$client = new JdClient($config);

// 加载请求类
require_once 'jos-php-open-api-sdk-2.0/jd/request/UnionOpenGoodsJingfenQueryRequest.php';
require_once 'jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenGoodsJingfenQuery/GoodsReq.php';

try {
    // 创建请求
    $req = new UnionOpenGoodsJingfenQueryRequest();
    $goodsReq = new \UnionOpenGoodsJingfenQuery\GoodsReq();

    $goodsReq->setEliteId(1);        // 好券商品
    $goodsReq->setPageIndex(1);      // 第一页
    $goodsReq->setPageSize(20);      // 每页20个

    $req->setGoodsReq($goodsReq->getInstance());
    $req->setVersion("1.0");

    // 执行请求
    $response = $client->execute($req);

    // 处理结果
    if (isset($response['data']) && !empty($response['data'])) {
        foreach ($response['data'] as $item) {
            echo "商品：" . $item['skuName'] . "\n";
            echo "价格：¥" . $item['priceInfo']['price'] . "\n";
            echo "佣金：¥" . $item['commissionInfo']['commission'] . "\n";
            echo "联盟ID：" . $item['itemId'] . "\n\n";
        }
    }

} catch (Exception $e) {
    echo "API调用失败：" . $e->getMessage() . "\n";
}
?>
```

## 权限申请状态

### ✅ 已开通且可用的API
- ✅ `jd.union.open.goods.rank.query` - 商品排行榜查询
- ✅ `jd.union.open.goods.jingfen.query` - 精选商品查询
- ✅ `jd.union.open.goods.material.query` - 商品物料查询
- ✅ `jd.union.open.activity.query` - 活动查询

### ❌ 需要申请权限的API
- ❌ `jd.union.open.promotion.byunionid.get` - 推广链接生成（403权限错误）
- ❌ `jd.union.open.promotion.bysubunionid.get` - 推广链接生成（403权限错误）
- ❌ `jd.union.open.promotion.applet.get` - 小程序推广链接（403权限错误）
- ❌ `jd.union.open.goods.bigfield.query` - 商品大字段查询（参数类型错误）

### 🔧 需要配置修复的API
- 🔧 `jd.union.open.promotion.common.get` - 通用推广链接生成（siteId类型问题）

### 申请建议
联系京东联盟业务团队申请以下核心API权限：

#### 高优先级
1. **推广链接生成API** - 用于生成带佣金的推广链接
   - `jd.union.open.promotion.byunionid.get`
   - `jd.union.open.promotion.bysubunionid.get`
   - `jd.union.open.promotion.applet.get`

#### 中优先级
2. **商品大字段查询API** - 用于获取商品详细信息
   - `jd.union.open.goods.bigfield.query`

#### 配置检查
3. **推广位配置** - 确认推广位类型支持相应推广方式
   - 检查siteId是否为网站/APP类型
   - 确认推广位权限设置

## 技术架构说明

### JdClient官方格式支持

我们对JdClient进行了重要升级，支持京东联盟官方标准格式：

#### 关键改进
1. **参数名称**：使用 `360buy_param_json` 替代 `param_json`
2. **请求方式**：支持GET请求替代POST请求
3. **时间戳格式**：支持毫秒精度和时区信息
4. **向后兼容**：保留原有格式支持

#### 配置方式
```php
$config = [
    'use_official_format' => true,  // 启用官方格式
    // ... 其他配置
];
```

## 更新日志

- **2025-07-17**：完成商品排行榜和精选商品查询API的测试和文档编写
- **2025-07-17**：修复JdClient官方格式支持，解决API调用问题
- **2025-07-17**：创建完整的API使用指南文档
- **2025-07-17**：新增商品物料查询API测试和文档
- **2025-07-17**：新增活动查询API测试和文档
- **2025-07-17**：完成推广链接生成API测试，确认权限申请需求
- **2025-07-17**：修复subUnionId参数类型问题，完善错误处理机制
- **2025-07-17**：更新API权限状态和申请建议
