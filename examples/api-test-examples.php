<?php
/**
 * 京东联盟API测试示例
 * 
 * 本文件包含了京东联盟API的完整使用示例
 * 运行前请确保已正确配置.env文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入必要的文件
require_once '../src/Utils/Logger.php';
require_once '../src/Utils/Helper.php';
require_once '../src/Utils/EnvLoader.php';
require_once '../src/Services/CacheService.php';
require_once '../src/Services/JdClient.php';

use Utils\Logger;
use Utils\Helper;
use Utils\EnvLoader;
use Services\JdClient;

echo "=== 京东联盟API测试示例 ===\n\n";

// 加载环境变量
EnvLoader::load();

try {
    $config = EnvLoader::getJdUnionConfig();
    
    // 启用官方格式（重要！）
    $config['use_official_format'] = true;
    
    echo "配置信息:\n";
    echo "- APP Key: " . substr($config['app_key'], 0, 8) . "***\n";
    echo "- 使用官方格式: " . ($config['use_official_format'] ? '是' : '否') . "\n\n";
    
    // 创建客户端
    $client = new JdClient($config);
    
    // 示例1：获取商品排行榜
    echo "=== 示例1：获取数码家电高佣排行榜 ===\n";
    
    // 加载请求类
    require_once '../jos-php-open-api-sdk-2.0/jd/request/UnionOpenGoodsRankQueryRequest.php';
    require_once '../jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenGoodsRankQuery/RankGoodsReq.php';
    
    $req1 = new UnionOpenGoodsRankQueryRequest();
    $rankGoodsReq = new \UnionOpenGoodsRankQuery\RankGoodsReq();
    $rankGoodsReq->setRankId(200006);    // 数码家电
    $rankGoodsReq->setSortType(2);       // 高佣排序
    $rankGoodsReq->setPageIndex(1);      // 第一页
    $rankGoodsReq->setPageSize(5);       // 每页5个
    
    $req1->setRankGoodsReq($rankGoodsReq->getInstance());
    $req1->setVersion("1.0");
    
    $response1 = $client->execute($req1);
    
    if (isset($response1['data']) && !empty($response1['data'])) {
        echo "成功获取 " . count($response1['data']) . " 个数码家电商品:\n";
        foreach (array_slice($response1['data'], 0, 3) as $index => $item) {
            echo ($index + 1) . ". " . mb_substr($item['skuName'], 0, 30) . "...\n";
            echo "   价格: ¥" . $item['purchasePriceInfo']['purchasePrice'] . "\n";
            echo "   佣金: ¥" . $item['commission'] . " (" . $item['commissionShare'] . "%)\n";
            echo "   联盟ID: " . $item['itemId'] . "\n\n";
        }
    } else {
        echo "未获取到排行榜数据\n\n";
    }
    
    // 示例2：获取精选商品
    echo "=== 示例2：获取好券商品 ===\n";
    
    // 加载请求类
    require_once '../jos-php-open-api-sdk-2.0/jd/request/UnionOpenGoodsJingfenQueryRequest.php';
    require_once '../jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenGoodsJingfenQuery/GoodsReq.php';
    
    $req2 = new UnionOpenGoodsJingfenQueryRequest();
    $goodsReq = new \UnionOpenGoodsJingfenQuery\GoodsReq();
    $goodsReq->setEliteId(1);           // 好券商品
    $goodsReq->setPageIndex(1);         // 第一页
    $goodsReq->setPageSize(5);          // 每页5个
    // 注意：不要设置pid参数
    
    $req2->setGoodsReq($goodsReq->getInstance());
    $req2->setVersion("1.0");
    
    $response2 = $client->execute($req2);
    
    if (isset($response2['data']) && !empty($response2['data'])) {
        echo "成功获取 " . count($response2['data']) . " 个好券商品:\n";
        foreach (array_slice($response2['data'], 0, 3) as $index => $item) {
            echo ($index + 1) . ". " . mb_substr($item['skuName'], 0, 30) . "...\n";
            echo "   原价: ¥" . $item['priceInfo']['price'] . "\n";
            echo "   券后价: ¥" . $item['priceInfo']['lowestCouponPrice'] . "\n";
            echo "   佣金: ¥" . $item['commissionInfo']['commission'] . " (" . $item['commissionInfo']['commissionShare'] . "%)\n";
            echo "   联盟ID: " . $item['itemId'] . "\n";
            
            // 显示优惠券信息
            if (isset($item['couponInfo']['couponList'][0])) {
                $coupon = $item['couponInfo']['couponList'][0];
                echo "   优惠券: 满¥" . $coupon['quota'] . "减¥" . $coupon['discount'] . "\n";
            }
            echo "\n";
        }
    } else {
        echo "未获取到精选商品数据\n\n";
    }
    
    // 示例3：获取9.9包邮商品
    echo "=== 示例3：获取9.9包邮商品 ===\n";
    
    $req3 = new UnionOpenGoodsJingfenQueryRequest();
    $goodsReq3 = new \UnionOpenGoodsJingfenQuery\GoodsReq();
    $goodsReq3->setEliteId(10);         // 9.9包邮
    $goodsReq3->setPageIndex(1);
    $goodsReq3->setPageSize(5);
    
    $req3->setGoodsReq($goodsReq3->getInstance());
    $req3->setVersion("1.0");
    
    $response3 = $client->execute($req3);
    
    if (isset($response3['data']) && !empty($response3['data'])) {
        echo "成功获取 " . count($response3['data']) . " 个9.9包邮商品:\n";
        foreach (array_slice($response3['data'], 0, 3) as $index => $item) {
            echo ($index + 1) . ". " . mb_substr($item['skuName'], 0, 30) . "...\n";
            echo "   价格: ¥" . $item['priceInfo']['price'] . "\n";
            echo "   佣金: ¥" . $item['commissionInfo']['commission'] . "\n";
            echo "   联盟ID: " . $item['itemId'] . "\n\n";
        }
    } else {
        echo "未获取到9.9包邮商品数据\n\n";
    }
    
    // 示例4：批量获取多个榜单
    echo "=== 示例4：批量获取多个榜单 ===\n";
    
    $categories = [
        200001 => '食品酒水',
        200003 => '个护美妆',
        200007 => '家居日用'
    ];
    
    foreach ($categories as $rankId => $categoryName) {
        echo "获取{$categoryName}榜单...\n";
        
        $req4 = new UnionOpenGoodsRankQueryRequest();
        $rankGoodsReq4 = new \UnionOpenGoodsRankQuery\RankGoodsReq();
        $rankGoodsReq4->setRankId($rankId);
        $rankGoodsReq4->setSortType(3);      // 24小时排序
        $rankGoodsReq4->setPageSize(3);      // 每个榜单3个商品
        
        $req4->setRankGoodsReq($rankGoodsReq4->getInstance());
        $req4->setVersion("1.0");
        
        $response4 = $client->execute($req4);
        
        if (isset($response4['data']) && !empty($response4['data'])) {
            echo "  - 获取到 " . count($response4['data']) . " 个{$categoryName}商品\n";
            $topItem = $response4['data'][0];
            echo "  - 榜首商品: " . mb_substr($topItem['skuName'], 0, 25) . "...\n";
            echo "  - 价格: ¥" . $topItem['purchasePriceInfo']['purchasePrice'] . "\n";
        } else {
            echo "  - 未获取到{$categoryName}数据\n";
        }
        echo "\n";
        
        // 避免请求过于频繁
        sleep(1);
    }
    
    // 示例5：获取商品物料
    echo "=== 示例5：获取商品物料 ===\n";

    require_once '../jos-php-open-api-sdk-2.0/jd/request/UnionOpenGoodsMaterialQueryRequest.php';
    require_once '../jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenGoodsMaterialQuery/GoodsReq.php';

    $req5 = new UnionOpenGoodsMaterialQueryRequest();
    $goodsReq5 = new \UnionOpenGoodsMaterialQuery\GoodsReq();
    $goodsReq5->setEliteId(1);          // 猜你喜欢
    $goodsReq5->setPageIndex(1);
    $goodsReq5->setPageSize(5);
    // 不设置pid参数

    $req5->setGoodsReq($goodsReq5->getInstance());
    $req5->setVersion("1.0");

    $response5 = $client->execute($req5);

    if (isset($response5['data']) && !empty($response5['data'])) {
        echo "成功获取 " . count($response5['data']) . " 个物料商品:\n";
        foreach (array_slice($response5['data'], 0, 2) as $index => $item) {
            echo ($index + 1) . ". " . mb_substr($item['skuName'], 0, 30) . "...\n";
            echo "   价格: ¥" . $item['priceInfo']['price'] . "\n";
            echo "   佣金: ¥" . $item['commissionInfo']['commission'] . "\n";
            echo "   推广链接: " . substr($item['promotionInfo']['clickURL'], 0, 50) . "...\n\n";
        }
    } else {
        echo "未获取到物料商品数据\n\n";
    }

    // 示例6：获取活动信息
    echo "=== 示例6：获取活动信息 ===\n";

    require_once '../jos-php-open-api-sdk-2.0/jd/request/UnionOpenActivityQueryRequest.php';
    require_once '../jos-php-open-api-sdk-2.0/jd/request/domain/UnionOpenActivityQuery/ActivityReq.php';

    $req6 = new UnionOpenActivityQueryRequest();
    $activityReq = new \UnionOpenActivityQuery\ActivityReq();
    $activityReq->setPageIndex(1);
    $activityReq->setPageSize(5);

    $req6->setActivityReq($activityReq->getInstance());
    $req6->setVersion("1.0");

    $response6 = $client->execute($req6);

    if (isset($response6['data']) && !empty($response6['data'])) {
        echo "成功获取 " . count($response6['data']) . " 个活动:\n";
        foreach (array_slice($response6['data'], 0, 2) as $index => $activity) {
            echo ($index + 1) . ". " . $activity['title'] . "\n";
            echo "   优势: " . $activity['advantage'] . "\n";
            echo "   状态: " . $activity['actStatus'] . "\n";
            echo "   移动端链接: " . $activity['urlM'] . "\n\n";
        }
        echo "总活动数: " . (isset($response6['totalCount']) ? $response6['totalCount'] : '未知') . "\n\n";
    } else {
        echo "未获取到活动数据\n\n";
    }

    echo "=== 测试完成 ===\n";
    echo "所有API调用均成功！您可以基于这些示例开发自己的应用。\n\n";

    echo "📚 更多信息请参考:\n";
    echo "- 完整API文档: docs/jd-union-api-guide.md\n";
    echo "- 快速参考: docs/jd-union-api-quick-reference.md\n";

    echo "\n🎯 当前可用功能:\n";
    echo "✅ 商品排行榜查询 - 获取热销商品\n";
    echo "✅ 精选商品查询 - 获取好券商品\n";
    echo "✅ 商品物料查询 - 获取推广链接\n";
    echo "✅ 活动查询 - 获取营销活动\n";
    echo "⏳ 推广链接生成 - 需要申请API权限\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
    
    echo "\n🔧 故障排除建议:\n";
    echo "1. 检查.env文件配置是否正确\n";
    echo "2. 确认京东联盟API权限已开通\n";
    echo "3. 验证网络连接是否正常\n";
    echo "4. 查看详细错误信息进行调试\n";
}
?>
