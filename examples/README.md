# 京东联盟API示例代码

本目录包含了京东联盟API的完整使用示例，帮助您快速上手和测试API功能。

## 📁 文件说明

### `api-test-examples.php`
完整的API测试示例，包含：
- 商品排行榜查询示例
- 精选商品查询示例
- 9.9包邮商品查询示例
- 批量获取多个榜单示例
- 商品物料查询示例
- 活动查询示例

## 🚀 运行示例

### 1. 环境准备
确保已正确配置 `.env` 文件：
```env
JD_UNION_APP_KEY=your_app_key
JD_UNION_APP_SECRET=your_app_secret
JD_UNION_SITE_ID=your_site_id
JD_UNION_POSITION_ID=your_position_id
JD_UNION_UNION_ID=your_union_id
```

### 2. 运行测试
```bash
cd examples
php api-test-examples.php
```

### 3. 预期输出
```
=== 京东联盟API测试示例 ===

配置信息:
- APP Key: f6bd0b02***
- 使用官方格式: 是

=== 示例1：获取数码家电高佣排行榜 ===
成功获取 5 个数码家电商品:
1. 小米14 Ultra 徕卡光学镜头 骁龙8 Gen3...
   价格: ¥5999.00
   佣金: ¥59.99 (1.0%)
   联盟ID: abc123def456...

=== 示例2：获取好券商品 ===
成功获取 5 个好券商品:
1. 【王牌单品】儿童磁性数字小火车玩具...
   原价: ¥7.99
   券后价: ¥5.99
   佣金: ¥2.40 (30.0%)
   联盟ID: ogP6162xP0Sa...
   优惠券: 满¥2.01减¥2.0

...
```

## 📋 示例说明

### 示例1：商品排行榜查询
```php
// 获取数码家电高佣排行榜
$rankGoodsReq->setRankId(200006);    // 数码家电
$rankGoodsReq->setSortType(2);       // 高佣排序
```

**支持的榜单类型**：
- 200000: 全部
- 200001: 食品酒水
- 200002: 家庭清洁
- 200003: 个护美妆
- 200004: 医药保健
- 200005: 生鲜
- 200006: 数码家电
- 200007: 家居日用
- 200008: 时尚生活

### 示例2：精选商品查询
```php
// 获取好券商品
$goodsReq->setEliteId(1);           // 好券商品
// 重要：不要设置pid参数
```

**支持的精选池**：
- 1: 好券商品
- 2: 精选卖场
- 10: 9.9包邮
- 22: 京东爆品

### 示例3：批量处理
展示如何批量获取多个榜单数据，适用于需要大量商品数据的场景。

### 示例4：商品物料查询
```php
// 获取猜你喜欢商品（包含推广链接）
$goodsReq->setEliteId(1);           // 猜你喜欢
// 不设置pid参数
```

**支持的频道类型**：
- 1: 猜你喜欢 ✅
- 2: 实时热销 ❌（需要PID）
- 3: 大额券 ❌（需要PID）
- 4: 9.9包邮 ❌（需要PID）
- 13270: 国补商品 ❌（需要PID）

### 示例5：活动查询
```php
// 获取活动列表
$activityReq->setPageIndex(1);      // 页码
$activityReq->setPageSize(20);      // 每页数量
$activityReq->setPoolId(1);         // 活动池ID（可选）
```

**活动类型**：
- 国补活动：超级国补日、3C数码国补日
- 品类活动：低温乳品品类日、京东超市水饮节
- 促销活动：双11、心动购物季

## ⚠️ 重要提示

### 必须启用官方格式
```php
$config['use_official_format'] = true;  // 必须设置
```

### 精选商品查询注意事项
- ✅ 不要设置 `pid` 参数
- ✅ 使用合适的 `pageSize`（建议10-50）
- ✅ 实现适当的延时避免频繁调用

### 错误处理
示例代码包含完整的错误处理逻辑：
```php
try {
    $response = $client->execute($req);
} catch (Exception $e) {
    echo "API调用失败: " . $e->getMessage() . "\n";
}
```

## 🔧 故障排除

### 常见问题

1. **403权限错误**
   ```
   解决方案：联系京东联盟申请API权限
   ```

2. **400参数错误**
   ```
   解决方案：检查参数格式，确保必填参数已设置
   ```

3. **返回空数据**
   ```
   解决方案：检查eliteId是否正确，不要设置pid参数
   ```

4. **网络连接错误**
   ```
   解决方案：检查网络连接，确认防火墙设置
   ```

### 调试技巧

1. **查看原始响应**
   ```php
   // JdClient会自动输出调试信息
   echo "原始响应: " . $response . "\n";
   ```

2. **验证配置**
   ```php
   var_dump($config);  // 检查配置是否正确加载
   ```

3. **分步测试**
   ```php
   // 先测试简单的API，再测试复杂的
   ```

## 📚 相关文档

- [完整API使用指南](../docs/jd-union-api-guide.md)
- [API快速参考](../docs/jd-union-api-quick-reference.md)
- [项目README](../README.md)

## 🔄 更新日志

- **2025-07-17**: 创建API测试示例和文档
- **2025-07-17**: 添加批量处理和错误处理示例
- **2025-07-17**: 新增商品物料查询和活动查询示例
- **2025-07-17**: 更新API权限状态和使用建议
