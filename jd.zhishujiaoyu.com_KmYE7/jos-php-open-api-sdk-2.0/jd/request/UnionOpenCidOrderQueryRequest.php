<?php
class UnionOpenCidOrderQueryRequest
{

    public function __construct()
    {
         $this->version = "1.0";
    }

	private $apiParas = array();
	
	public function getApiMethodName(){
	  return "jd.union.open.cid.order.query";
	}
	
	public function getApiParas(){
        if(empty($this->apiParas)){
	        return "{}";
	    }
		return $this->apiParas;
	}
	
	public function check(){
		
	}
	
    public function putOtherTextParam($key, $value){
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}

    private $version;

    public function setVersion($version){
        $this->version = $version;
    }

    public function getVersion(){
        return $this->version;
    }
    private  $cidOrderReq;

    public function setCidOrderReq($cidOrderReq){
        $this->apiParas['cidOrderReq'] = $cidOrderReq;
    }
    public function getCidOrderReq(){
        return $this->apiParas['cidOrderReq'];
    }
}

?>