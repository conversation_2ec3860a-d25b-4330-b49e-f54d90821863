<?php
class WareProductsortGetRequest
{


	private $apiParas = array();
	
	public function getApiMethodName(){
	  return "jingdong.ware.productsort.get";
	}
	
	public function getApiParas(){
	    if(empty($this->apiParas)){
            return "{}";
        }
        return $this->apiParas;
	}
	
	public function check(){
		
	}
	
	public function putOtherTextParam($key, $value){
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}

    private $version;

    public function setVersion($version){
        $this->version = $version;
    }

    public function getVersion(){
        return $this->version;
    }
                                                             	                        	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               private $productSortIds;
                              public function setProductSortIds($productSortIds ){
                 $this->productSortIds=$productSortIds;
                 $this->apiParas["product_sort_ids"] = $productSortIds;
              }

              public function getProductSortIds(){
              	return $this->productSortIds;
              }
                                                                                                                }





        
 

