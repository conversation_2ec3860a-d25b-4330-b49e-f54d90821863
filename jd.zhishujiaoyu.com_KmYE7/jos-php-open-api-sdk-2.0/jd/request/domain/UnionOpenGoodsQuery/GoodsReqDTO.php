<?php
namespace UnionOpenGoodsQuery;
class GoodsReqDTO{

    private $params=array();

    function __construct(){
        $this->params["@type"]="com.jd.union.open.gateway.api.dto.goods.base.GoodsReq";
    }
        
    private $cid1;
    
    public function setCid1($cid1){
        $this->params['cid1'] = $cid1;
    }

    public function getCid1(){
        return $this->cid1;
    }
            
    private $cid2;
    
    public function setCid2($cid2){
        $this->params['cid2'] = $cid2;
    }

    public function getCid2(){
        return $this->cid2;
    }
            
    private $cid3;
    
    public function setCid3($cid3){
        $this->params['cid3'] = $cid3;
    }

    public function getCid3(){
        return $this->cid3;
    }
            
    private $pageIndex;
    
    public function setPageIndex($pageIndex){
        $this->params['pageIndex'] = $pageIndex;
    }

    public function getPageIndex(){
        return $this->pageIndex;
    }
            
    private $pageSize;
    
    public function setPageSize($pageSize){
        $this->params['pageSize'] = $pageSize;
    }

    public function getPageSize(){
        return $this->pageSize;
    }
            
    private $skuIds;
    
    public function setSkuIds($skuIds){
        $this->params['skuIds'] = $skuIds;
    }

    public function getSkuIds(){
        return $this->skuIds;
    }
            
    private $keyword;
    
    public function setKeyword($keyword){
        $this->params['keyword'] = $keyword;
    }

    public function getKeyword(){
        return $this->keyword;
    }
            
    private $pricefrom;
    
    public function setPricefrom($pricefrom){
        $this->params['pricefrom'] = $pricefrom;
    }

    public function getPricefrom(){
        return $this->pricefrom;
    }
            
    private $priceto;
    
    public function setPriceto($priceto){
        $this->params['priceto'] = $priceto;
    }

    public function getPriceto(){
        return $this->priceto;
    }
            
    private $commissionShareStart;
    
    public function setCommissionShareStart($commissionShareStart){
        $this->params['commissionShareStart'] = $commissionShareStart;
    }

    public function getCommissionShareStart(){
        return $this->commissionShareStart;
    }
            
    private $commissionShareEnd;
    
    public function setCommissionShareEnd($commissionShareEnd){
        $this->params['commissionShareEnd'] = $commissionShareEnd;
    }

    public function getCommissionShareEnd(){
        return $this->commissionShareEnd;
    }
            
    private $owner;
    
    public function setOwner($owner){
        $this->params['owner'] = $owner;
    }

    public function getOwner(){
        return $this->owner;
    }
            
    private $sortName;
    
    public function setSortName($sortName){
        $this->params['sortName'] = $sortName;
    }

    public function getSortName(){
        return $this->sortName;
    }
            
    private $sort;
    
    public function setSort($sort){
        $this->params['sort'] = $sort;
    }

    public function getSort(){
        return $this->sort;
    }
            
    private $isCoupon;
    
    public function setIsCoupon($isCoupon){
        $this->params['isCoupon'] = $isCoupon;
    }

    public function getIsCoupon(){
        return $this->isCoupon;
    }
            
    private $isPG;
    
    public function setIsPG($isPG){
        $this->params['isPG'] = $isPG;
    }

    public function getIsPG(){
        return $this->isPG;
    }
            
    private $pingouPriceStart;
    
    public function setPingouPriceStart($pingouPriceStart){
        $this->params['pingouPriceStart'] = $pingouPriceStart;
    }

    public function getPingouPriceStart(){
        return $this->pingouPriceStart;
    }
            
    private $pingouPriceEnd;
    
    public function setPingouPriceEnd($pingouPriceEnd){
        $this->params['pingouPriceEnd'] = $pingouPriceEnd;
    }

    public function getPingouPriceEnd(){
        return $this->pingouPriceEnd;
    }
            
    private $isHot;
    
    public function setIsHot($isHot){
        $this->params['isHot'] = $isHot;
    }

    public function getIsHot(){
        return $this->isHot;
    }
            
    private $brandCode;
    
    public function setBrandCode($brandCode){
        $this->params['brandCode'] = $brandCode;
    }

    public function getBrandCode(){
        return $this->brandCode;
    }
            
    private $shopId;
    
    public function setShopId($shopId){
        $this->params['shopId'] = $shopId;
    }

    public function getShopId(){
        return $this->shopId;
    }
            
    private $hasContent;
    
    public function setHasContent($hasContent){
        $this->params['hasContent'] = $hasContent;
    }

    public function getHasContent(){
        return $this->hasContent;
    }
            
    private $hasBestCoupon;
    
    public function setHasBestCoupon($hasBestCoupon){
        $this->params['hasBestCoupon'] = $hasBestCoupon;
    }

    public function getHasBestCoupon(){
        return $this->hasBestCoupon;
    }
            
    private $pid;
    
    public function setPid($pid){
        $this->params['pid'] = $pid;
    }

    public function getPid(){
        return $this->pid;
    }
            
    private $fields;
    
    public function setFields($fields){
        $this->params['fields'] = $fields;
    }

    public function getFields(){
        return $this->fields;
    }
            
    private $forbidTypes;
    
    public function setForbidTypes($forbidTypes){
        $this->params['forbidTypes'] = $forbidTypes;
    }

    public function getForbidTypes(){
        return $this->forbidTypes;
    }
            
    private $jxFlags;
    
    public function setJxFlags($jxFlags){
        $this->params['jxFlags'] = $jxFlags;
    }

    public function getJxFlags(){
        return $this->jxFlags;
    }
            
    private $shopLevelFrom;
    
    public function setShopLevelFrom($shopLevelFrom){
        $this->params['shopLevelFrom'] = $shopLevelFrom;
    }

    public function getShopLevelFrom(){
        return $this->shopLevelFrom;
    }
            
    private $isbn;
    
    public function setIsbn($isbn){
        $this->params['isbn'] = $isbn;
    }

    public function getIsbn(){
        return $this->isbn;
    }
            
    private $spuId;
    
    public function setSpuId($spuId){
        $this->params['spuId'] = $spuId;
    }

    public function getSpuId(){
        return $this->spuId;
    }
            
    private $couponUrl;
    
    public function setCouponUrl($couponUrl){
        $this->params['couponUrl'] = $couponUrl;
    }

    public function getCouponUrl(){
        return $this->couponUrl;
    }
            
    private $deliveryType;
    
    public function setDeliveryType($deliveryType){
        $this->params['deliveryType'] = $deliveryType;
    }

    public function getDeliveryType(){
        return $this->deliveryType;
    }
            
    private $eliteType;
    
    public function setEliteType($eliteType){
        $this->params['eliteType'] = $eliteType;
    }

    public function getEliteType(){
        return $this->eliteType;
    }
            
    private $isSeckill;
    
    public function setIsSeckill($isSeckill){
        $this->params['isSeckill'] = $isSeckill;
    }

    public function getIsSeckill(){
        return $this->isSeckill;
    }
            
    private $isPresale;
    
    public function setIsPresale($isPresale){
        $this->params['isPresale'] = $isPresale;
    }

    public function getIsPresale(){
        return $this->isPresale;
    }
            
    private $isReserve;
    
    public function setIsReserve($isReserve){
        $this->params['isReserve'] = $isReserve;
    }

    public function getIsReserve(){
        return $this->isReserve;
    }
            
    private $bonusId;
    
    public function setBonusId($bonusId){
        $this->params['bonusId'] = $bonusId;
    }

    public function getBonusId(){
        return $this->bonusId;
    }
            
    private $area;
    
    public function setArea($area){
        $this->params['area'] = $area;
    }

    public function getArea(){
        return $this->area;
    }
            
    private $isOversea;
    
    public function setIsOversea($isOversea){
        $this->params['isOversea'] = $isOversea;
    }

    public function getIsOversea(){
        return $this->isOversea;
    }
            
    private $userIdType;
    
    public function setUserIdType($userIdType){
        $this->params['userIdType'] = $userIdType;
    }

    public function getUserIdType(){
        return $this->userIdType;
    }
            
    private $userId;
    
    public function setUserId($userId){
        $this->params['userId'] = $userId;
    }

    public function getUserId(){
        return $this->userId;
    }
            
    private $channelId;
    
    public function setChannelId($channelId){
        $this->params['channelId'] = $channelId;
    }

    public function getChannelId(){
        return $this->channelId;
    }
            
    private $ip;
    
    public function setIp($ip){
        $this->params['ip'] = $ip;
    }

    public function getIp(){
        return $this->ip;
    }
            
    private $provinceId;
    
    public function setProvinceId($provinceId){
        $this->params['provinceId'] = $provinceId;
    }

    public function getProvinceId(){
        return $this->provinceId;
    }
            
    private $cityId;
    
    public function setCityId($cityId){
        $this->params['cityId'] = $cityId;
    }

    public function getCityId(){
        return $this->cityId;
    }
            
    private $countryId;
    
    public function setCountryId($countryId){
        $this->params['countryId'] = $countryId;
    }

    public function getCountryId(){
        return $this->countryId;
    }
            
    private $townId;
    
    public function setTownId($townId){
        $this->params['townId'] = $townId;
    }

    public function getTownId(){
        return $this->townId;
    }
            
    private $itemIds;
    
    public function setItemIds($itemIds){
        $this->params['itemIds'] = $itemIds;
    }

    public function getItemIds(){
        return $this->itemIds;
    }
            
    private $sceneId;
    
    public function setSceneId($sceneId){
        $this->params['sceneId'] = $sceneId;
    }

    public function getSceneId(){
        return $this->sceneId;
    }
            
    private $pin;
    
    public function setPin($pin){
        $this->params['pin'] = $pin;
    }

    public function getPin(){
        return $this->pin;
    }
            
    private $searchPosition;
    
    public function setSearchPosition($searchPosition){
        $this->params['searchPosition'] = $searchPosition;
    }

    public function getSearchPosition(){
        return $this->searchPosition;
    }
    
    function getInstance(){
        return $this->params;
    }

}

?>