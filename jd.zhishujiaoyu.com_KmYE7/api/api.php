<?php
/**
 * API控制器
 * 处理系统相关的API请求
 */

use Utils\Logger;
use Utils\EnvLoader;
use Services\DatabaseService;
use Services\CacheService;
use Services\JDUnionService;

class ApiController extends BaseController
{
    /**
     * 系统测试接口
     */
    public function test()
    {
        try {
            $tests = [];
            
            // 测试数据库连接
            try {
                $db = DatabaseService::getInstance();
                $tests['database'] = [
                    'status' => $db->testConnection() ? 'ok' : 'failed',
                    'config' => $db->getStatus()
                ];
            } catch (Exception $e) {
                $tests['database'] = [
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
            
            // 测试缓存服务
            try {
                $cache = new CacheService();
                $testKey = 'test_' . time();
                $testValue = 'test_value';
                
                $cache->set($testKey, $testValue, 60);
                $retrieved = $cache->get($testKey);
                $cache->delete($testKey);
                
                $tests['cache'] = [
                    'status' => ($retrieved === $testValue) ? 'ok' : 'failed',
                    'stats' => $cache->getStats()
                ];
            } catch (Exception $e) {
                $tests['cache'] = [
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
            
            // 测试京东联盟API
            try {
                $jdConfig = EnvLoader::getJdUnionConfig();
                if (!empty($jdConfig['app_key']) && !empty($jdConfig['app_secret'])) {
                    $jdService = new JDUnionService();
                    $tests['jd_union'] = [
                        'status' => 'configured',
                        'config' => [
                            'app_key' => substr($jdConfig['app_key'], 0, 8) . '***',
                            'enabled' => $jdConfig['enabled']
                        ]
                    ];
                } else {
                    $tests['jd_union'] = [
                        'status' => 'not_configured',
                        'message' => '京东联盟API密钥未配置'
                    ];
                }
            } catch (Exception $e) {
                $tests['jd_union'] = [
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
            
            // 测试日志系统
            try {
                Logger::info('系统测试', ['test_time' => time()]);
                $tests['logger'] = [
                    'status' => 'ok',
                    'config' => EnvLoader::getLogConfig()
                ];
            } catch (Exception $e) {
                $tests['logger'] = [
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
            
            // 计算总体状态
            $overallStatus = 'ok';
            foreach ($tests as $test) {
                if ($test['status'] !== 'ok' && $test['status'] !== 'configured') {
                    $overallStatus = 'failed';
                    break;
                }
            }
            
            return $this->success([
                'overall_status' => $overallStatus,
                'tests' => $tests,
                'system_info' => [
                    'php_version' => PHP_VERSION,
                    'memory_usage' => memory_get_usage(true),
                    'memory_limit' => ini_get('memory_limit'),
                    'max_execution_time' => ini_get('max_execution_time'),
                    'timezone' => date_default_timezone_get(),
                    'server_time' => date('Y-m-d H:i:s'),
                ]
            ], '系统测试完成');
            
        } catch (Exception $e) {
            Logger::error('系统测试失败', ['error' => $e->getMessage()]);
            return $this->error('系统测试失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 系统状态接口
     */
    public function status()
    {
        try {
            $status = [
                'service' => 'jd-union-service',
                'version' => '1.0.0',
                'status' => 'running',
                'uptime' => $this->getUptime(),
                'timestamp' => time(),
                'datetime' => date('Y-m-d H:i:s'),
            ];
            
            // 获取系统资源使用情况
            $status['resources'] = [
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'memory_limit' => $this->parseMemoryLimit(ini_get('memory_limit')),
                'cpu_load' => $this->getCpuLoad(),
                'disk_usage' => $this->getDiskUsage(),
            ];
            
            // 获取服务状态
            $status['services'] = [
                'database' => $this->getDatabaseStatus(),
                'cache' => $this->getCacheStatus(),
                'jd_union' => $this->getJdUnionStatus(),
            ];
            
            // 获取配置信息
            $status['config'] = [
                'debug' => EnvLoader::isDebug(),
                'timezone' => EnvLoader::getTimezone(),
                'log_level' => EnvLoader::get('LOG_LEVEL', 'info'),
                'cache_driver' => EnvLoader::get('CACHE_DRIVER', 'file'),
            ];
            
            return $this->success($status, '系统状态正常');
            
        } catch (Exception $e) {
            Logger::error('获取系统状态失败', ['error' => $e->getMessage()]);
            return $this->error('获取系统状态失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取系统运行时间
     */
    private function getUptime()
    {
        if (function_exists('sys_getloadavg') && file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptime = floatval(explode(' ', $uptime)[0]);
            return round($uptime);
        }
        return null;
    }
    
    /**
     * 解析内存限制
     */
    private function parseMemoryLimit($limit)
    {
        if ($limit === '-1') {
            return -1;
        }
        
        $unit = strtolower(substr($limit, -1));
        $value = intval(substr($limit, 0, -1));
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return intval($limit);
        }
    }
    
    /**
     * 获取CPU负载
     */
    private function getCpuLoad()
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2],
            ];
        }
        return null;
    }
    
    /**
     * 获取磁盘使用情况
     */
    private function getDiskUsage()
    {
        $path = __DIR__ . '/..';
        
        return [
            'total' => disk_total_space($path),
            'free' => disk_free_space($path),
            'used' => disk_total_space($path) - disk_free_space($path),
        ];
    }
    
    /**
     * 获取数据库状态
     */
    private function getDatabaseStatus()
    {
        try {
            $db = DatabaseService::getInstance();
            return array_merge(['status' => 'ok'], $db->getStatus());
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取缓存状态
     */
    private function getCacheStatus()
    {
        try {
            $cache = new CacheService();
            return array_merge(['status' => 'ok'], $cache->getStats());
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取京东联盟状态
     */
    private function getJdUnionStatus()
    {
        try {
            $config = EnvLoader::getJdUnionConfig();
            
            if (empty($config['app_key']) || empty($config['app_secret'])) {
                return [
                    'status' => 'not_configured',
                    'message' => 'API密钥未配置'
                ];
            }
            
            return [
                'status' => 'configured',
                'enabled' => $config['enabled'],
                'app_key' => substr($config['app_key'], 0, 8) . '***',
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }
}
?>
