<?php
/**
 * 京东联盟授权Key API控制器
 * 使用授权Key直接调用京东联盟API
 */

use Utils\Logger;
use Utils\Helper;
use Services\JDUnionAuthService;

class JdAuthController extends BaseController
{
    private $jdAuthService;
    
    public function __construct()
    {
        parent::__construct();
        
        try {
            $this->jdAuthService = new JDUnionAuthService();
        } catch (Exception $e) {
            Logger::error('京东联盟授权服务初始化失败', ['error' => $e->getMessage()]);
            // 继续执行，在具体方法中处理错误
        }
    }
    
    /**
     * 获取商品列表（使用授权Key）
     * GET /api/jdauth/goods
     */
    public function goods()
    {
        if (!$this->jdAuthService) {
            return $this->error('京东联盟授权服务未正确配置', 500);
        }
        
        try {
            // 获取请求参数
            $params = [
                'eliteId' => intval($this->getParam('elite_id', 1)),
                'pageIndex' => intval($this->getParam('page', 1)),
                'pageSize' => intval($this->getParam('page_size', 20)),
                'sortName' => $this->getParam('sort_name', 'price'),
                'sort' => $this->getParam('sort', 'asc'),
            ];
            
            // 参数验证
            if ($params['pageIndex'] < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($params['pageSize'] < 1 || $params['pageSize'] > 100) {
                return $this->error('每页数量必须在1-100之间');
            }
            
            if (!in_array($params['eliteId'], [1, 2, 10, 15, 22, 23])) {
                return $this->error('无效的榜单类型');
            }
            
            // 调用服务获取数据
            $result = $this->jdAuthService->getGoodsList($params);
            
            // 格式化响应数据
            $response = [
                'list' => $result['data'] ?? [],
                'pagination' => [
                    'current_page' => $params['pageIndex'],
                    'page_size' => $params['pageSize'],
                    'total_count' => $result['total_count'] ?? 0,
                    'total_pages' => ceil(($result['total_count'] ?? 0) / $params['pageSize']),
                ],
                'params' => $params,
            ];
            
            Logger::info('获取商品列表成功（授权Key）', [
                'params' => $params,
                'count' => count($response['list'])
            ]);
            
            return $this->success($response, '获取商品列表成功');
            
        } catch (Exception $e) {
            Logger::error('获取商品列表失败（授权Key）', [
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取商品列表失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取商品详情（使用授权Key）
     * GET /api/jdauth/detail
     */
    public function detail()
    {
        if (!$this->jdAuthService) {
            return $this->error('京东联盟授权服务未正确配置', 500);
        }
        
        try {
            // 获取SKU ID参数
            $skuIds = $this->getParam('sku_ids');
            $skuId = $this->getParam('sku_id');
            
            if (empty($skuIds) && empty($skuId)) {
                return $this->error('请提供sku_id或sku_ids参数');
            }
            
            // 处理参数
            if (!empty($skuId)) {
                $skuIds = $skuId;
            }
            
            // 如果是数组，转换为逗号分隔的字符串
            if (is_array($skuIds)) {
                $skuIds = implode(',', $skuIds);
            }
            
            // 验证SKU ID格式
            $skuArray = explode(',', $skuIds);
            foreach ($skuArray as $sku) {
                if (!is_numeric(trim($sku))) {
                    return $this->error('SKU ID必须为数字');
                }
            }
            
            if (count($skuArray) > 20) {
                return $this->error('一次最多查询20个商品');
            }
            
            // 调用服务获取数据
            $result = $this->jdAuthService->getGoodsDetail($skuIds);
            
            Logger::info('获取商品详情成功（授权Key）', [
                'sku_ids' => $skuIds,
                'count' => count($result['data'] ?? [])
            ]);
            
            return $this->success($result['data'] ?? [], '获取商品详情成功');
            
        } catch (Exception $e) {
            Logger::error('获取商品详情失败（授权Key）', [
                'sku_ids' => $skuIds ?? '',
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取商品详情失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 搜索商品（使用授权Key）
     * GET /api/jdauth/search
     */
    public function search()
    {
        if (!$this->jdAuthService) {
            return $this->error('京东联盟授权服务未正确配置', 500);
        }
        
        try {
            // 获取搜索关键词
            $keyword = trim($this->getParam('keyword', ''));
            
            if (empty($keyword)) {
                return $this->error('请提供搜索关键词');
            }
            
            if (mb_strlen($keyword, 'UTF-8') < 2) {
                return $this->error('搜索关键词至少2个字符');
            }
            
            if (mb_strlen($keyword, 'UTF-8') > 50) {
                return $this->error('搜索关键词不能超过50个字符');
            }
            
            // 获取其他参数
            $params = [
                'keyword' => $keyword,
                'pageIndex' => intval($this->getParam('page', 1)),
                'pageSize' => intval($this->getParam('page_size', 20)),
                'sortName' => $this->getParam('sort_name', 'price'),
                'sort' => $this->getParam('sort', 'asc'),
                'cid1' => intval($this->getParam('cid1', 0)),
                'cid2' => intval($this->getParam('cid2', 0)),
                'cid3' => intval($this->getParam('cid3', 0)),
            ];
            
            // 参数验证
            if ($params['pageIndex'] < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($params['pageSize'] < 1 || $params['pageSize'] > 100) {
                return $this->error('每页数量必须在1-100之间');
            }
            
            // 移除空值参数
            $params = array_filter($params, function($value) {
                return $value !== 0 && $value !== '';
            });
            
            // 调用服务搜索商品
            $result = $this->jdAuthService->searchGoods($keyword, $params);
            
            // 格式化响应数据
            $response = [
                'list' => $result['data'] ?? [],
                'pagination' => [
                    'current_page' => $params['pageIndex'],
                    'page_size' => $params['pageSize'],
                    'total_count' => $result['total_count'] ?? 0,
                    'total_pages' => ceil(($result['total_count'] ?? 0) / $params['pageSize']),
                ],
                'search' => [
                    'keyword' => $keyword,
                    'params' => $params,
                ],
            ];
            
            Logger::info('搜索商品成功（授权Key）', [
                'keyword' => $keyword,
                'params' => $params,
                'count' => count($response['list'])
            ]);
            
            return $this->success($response, '搜索商品成功');
            
        } catch (Exception $e) {
            Logger::error('搜索商品失败（授权Key）', [
                'keyword' => $keyword ?? '',
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('搜索商品失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 生成推广链接（使用授权Key）
     * POST /api/jdauth/promotion
     */
    public function promotion()
    {
        if (!$this->jdAuthService) {
            return $this->error('京东联盟授权服务未正确配置', 500);
        }
        
        try {
            // 验证请求方法
            if ($this->request['method'] !== 'POST') {
                return $this->error('请使用POST方法', 405);
            }
            
            // 获取必需参数
            $materialId = $this->getParam('material_id');
            
            if (empty($materialId)) {
                return $this->error('请提供material_id参数（商品链接或SKU ID）');
            }
            
            // 获取可选参数
            $params = [
                'subUnionId' => $this->getParam('sub_union_id', ''),
                'ext1' => $this->getParam('ext1', ''),
                'couponUrl' => $this->getParam('coupon_url', ''),
            ];
            
            // 移除空值参数
            $params = array_filter($params, function($value) {
                return $value !== '';
            });
            
            // 调用服务生成推广链接
            $result = $this->jdAuthService->generatePromotionLink($materialId, $params);
            
            // 处理响应数据
            $data = $result['data'] ?? [];
            
            if (empty($data)) {
                return $this->error('生成推广链接失败，请检查商品ID是否正确');
            }
            
            // 格式化响应
            $response = [
                'material_id' => $materialId,
                'promotion_links' => $data,
                'params' => $params,
                'generated_at' => date('Y-m-d H:i:s'),
            ];
            
            Logger::info('生成推广链接成功（授权Key）', [
                'material_id' => $materialId,
                'params' => $params,
                'count' => count($data)
            ]);
            
            return $this->success($response, '生成推广链接成功');
            
        } catch (Exception $e) {
            Logger::error('生成推广链接失败（授权Key）', [
                'material_id' => $materialId ?? '',
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('生成推广链接失败: ' . $e->getMessage(), 500);
        }
    }
}
?>
