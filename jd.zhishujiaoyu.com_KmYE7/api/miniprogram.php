<?php
/**
 * 小程序API控制器
 * 提供小程序专用的商品和推广链接接口
 */

use Utils\Logger;
use Utils\Helper;
use Services\DatabaseService;
use Services\HaodankuService;
use Services\JdHotRankSyncService;

class MiniprogramController extends BaseController
{
    private $databaseService;
    private $haodankuService;
    private $syncService;
    
    public function __construct()
    {
        parent::__construct();
        
        try {
            $this->databaseService = DatabaseService::getInstance();
            $this->haodankuService = new HaodankuService();
            $this->syncService = new JdHotRankSyncService();
        } catch (Exception $e) {
            Logger::error('小程序服务初始化失败', ['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 获取京东热销商品列表（小程序专用）
     * GET /api/miniprogram/jd-hot-products
     */
    public function jdHotProducts()
    {
        try {
            // 获取请求参数
            $params = [
                'page' => intval($this->getParam('page', 1)),
                'page_size' => intval($this->getParam('page_size', 20)),
                'cid' => $this->getParam('cid', ''),
                'min_price' => floatval($this->getParam('min_price', 0)),
                'max_price' => floatval($this->getParam('max_price', 0)),
            ];
            
            // 参数验证
            if ($params['page'] < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($params['page_size'] < 1 || $params['page_size'] > 50) {
                return $this->error('每页数量必须在1-50之间');
            }
            
            // 构建筛选条件
            $filters = [];
            if (!empty($params['cid'])) {
                $filters['cid'] = $params['cid'];
            }
            if ($params['min_price'] > 0) {
                $filters['min_price'] = $params['min_price'];
            }
            if ($params['max_price'] > 0) {
                $filters['max_price'] = $params['max_price'];
            }
            
            // 从数据库获取商品列表
            $result = $this->databaseService->getJdHotProducts(
                $params['page'], 
                $params['page_size'], 
                $filters
            );
            
            // 格式化商品数据
            $formattedData = $this->formatProductsForMiniprogram($result['data']);
            
            $response = [
                'list' => $formattedData,
                'pagination' => [
                    'current_page' => $result['page'],
                    'page_size' => $result['page_size'],
                    'total_count' => $result['total'],
                    'total_pages' => $result['total_pages'],
                ],
                'filters' => $params,
                'last_sync_time' => $this->getLastSyncTime(),
            ];
            
            Logger::info('小程序获取京东热销商品成功', [
                'params' => $params,
                'count' => count($formattedData)
            ]);
            
            return $this->success($response, '获取商品列表成功');
            
        } catch (Exception $e) {
            Logger::error('小程序获取京东热销商品失败', [
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取商品列表失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 生成推广链接（小程序专用）
     * POST /api/miniprogram/generate-promotion-link
     */
    public function generatePromotionLink()
    {
        try {
            // 验证请求方法
            if ($this->request['method'] !== 'POST') {
                return $this->error('请使用POST方法', 405);
            }
            
            // 获取请求参数
            $skuId = $this->getParam('sku_id');
            $userId = $this->getParam('user_id', ''); // 可选的用户ID
            $couponUrl = $this->getParam('coupon_url', ''); // 可选的优惠券链接
            
            if (empty($skuId)) {
                return $this->error('请提供sku_id参数');
            }
            
            // 验证SKU ID格式
            if (!is_numeric($skuId)) {
                return $this->error('SKU ID必须为数字');
            }
            
            // 构建推广链接参数
            $promotionParams = [];
            if (!empty($couponUrl)) {
                $promotionParams['couponurl'] = $couponUrl;
            }
            
            // 调用好单库API生成推广链接
            $result = $this->haodankuService->generateJdPromotionLink($skuId, $promotionParams);
            
            if (empty($result)) {
                return $this->error('生成推广链接失败，请稍后重试');
            }
            
            // 提取推广链接信息
            $promotionUrl = $result['url'] ?? $result['promotion_url'] ?? '';
            $shortUrl = $result['short_url'] ?? '';
            
            if (empty($promotionUrl)) {
                return $this->error('获取推广链接失败');
            }
            
            // 保存推广链接记录到数据库
            try {
                $this->databaseService->savePromotionLink($skuId, $promotionUrl, $shortUrl, $userId);
            } catch (Exception $e) {
                // 记录日志但不影响返回结果
                Logger::warning('保存推广链接记录失败', [
                    'sku_id' => $skuId,
                    'error' => $e->getMessage()
                ]);
            }
            
            $response = [
                'sku_id' => $skuId,
                'promotion_url' => $promotionUrl,
                'short_url' => $shortUrl,
                'coupon_url' => $couponUrl,
                'generated_at' => date('Y-m-d H:i:s'),
            ];
            
            Logger::info('小程序生成推广链接成功', [
                'sku_id' => $skuId,
                'user_id' => $userId,
                'has_coupon' => !empty($couponUrl)
            ]);
            
            return $this->success($response, '生成推广链接成功');
            
        } catch (Exception $e) {
            Logger::error('小程序生成推广链接失败', [
                'sku_id' => $skuId ?? '',
                'user_id' => $userId ?? '',
                'error' => $e->getMessage()
            ]);
            
            return $this->error('生成推广链接失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 手动同步数据（管理员接口）
     * POST /api/miniprogram/sync-data
     */
    public function syncData()
    {
        try {
            // 验证请求方法
            if ($this->request['method'] !== 'POST') {
                return $this->error('请使用POST方法', 405);
            }
            
            // 可以添加管理员权限验证
            $adminKey = $this->getParam('admin_key');
            if ($adminKey !== 'your_admin_key_here') { // 请修改为实际的管理员密钥
                return $this->error('无权限执行此操作', 403);
            }
            
            // 获取同步参数
            $params = [
                'min_id' => intval($this->getParam('min_id', 1)),
                'cid' => intval($this->getParam('cid', 0)),
            ];
            
            // 执行同步
            $result = $this->syncService->syncJdHotRank($params);
            
            Logger::info('手动同步数据', [
                'params' => $params,
                'result' => $result
            ]);
            
            return $this->success($result['data'], $result['message']);
            
        } catch (Exception $e) {
            Logger::error('手动同步数据失败', [
                'error' => $e->getMessage()
            ]);
            
            return $this->error('同步数据失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取同步状态
     * GET /api/miniprogram/sync-status
     */
    public function syncStatus()
    {
        try {
            $limit = intval($this->getParam('limit', 5));
            $status = $this->syncService->getSyncStatus($limit);
            
            return $this->success($status, '获取同步状态成功');
            
        } catch (Exception $e) {
            Logger::error('获取同步状态失败', [
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取同步状态失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 格式化商品数据供小程序使用
     */
    private function formatProductsForMiniprogram($products)
    {
        $formatted = [];
        
        foreach ($products as $product) {
            $images = json_decode($product['jd_images'], true) ?: [];
            
            $formatted[] = [
                'id' => $product['id'],
                'sku_id' => $product['sku_id'],
                'name' => $product['goods_name'],
                'short_name' => $product['goods_name_short'],
                'description' => $product['goods_desc'],
                'original_price' => floatval($product['item_price']),
                'current_price' => floatval($product['item_end_price']),
                'sales_count' => intval($product['item_sale']),
                'main_image' => $product['item_pic'],
                'images' => array_filter($images), // 过滤空值
                'category_id' => $product['cid'],
                'commission_rate' => floatval($product['commission_share']),
                'commission_amount' => floatval($product['commission']),
                'coupon' => [
                    'url' => $product['coupon_url'],
                    'amount' => floatval($product['coupon_money']),
                    'quantity' => intval($product['coupon_num']),
                    'start_time' => intval($product['coupon_start_time']),
                    'end_time' => intval($product['coupon_end_time']),
                    'is_available' => $this->isCouponAvailable($product)
                ],
                'shop_name' => $product['shop_name'],
                'rank_position' => intval($product['rank_position']),
                'sync_time' => $product['sync_time'],
                'update_time' => $product['update_time'],
            ];
        }
        
        return $formatted;
    }
    
    /**
     * 检查优惠券是否可用
     */
    private function isCouponAvailable($product)
    {
        if (empty($product['coupon_url']) || floatval($product['coupon_money']) <= 0) {
            return false;
        }
        
        $now = time();
        $startTime = intval($product['coupon_start_time']);
        $endTime = intval($product['coupon_end_time']);
        
        if ($startTime > 0 && $now < $startTime) {
            return false; // 还未开始
        }
        
        if ($endTime > 0 && $now > $endTime) {
            return false; // 已过期
        }
        
        return true;
    }
    
    /**
     * 获取最后同步时间
     */
    private function getLastSyncTime()
    {
        try {
            $sql = "SELECT MAX(start_time) as last_sync_time 
                   FROM sync_logs 
                   WHERE sync_type = 'jd_hot_rank' AND sync_status = 'success'";
            
            $result = $this->databaseService->queryOne($sql);
            return $result['last_sync_time'] ?? null;
            
        } catch (Exception $e) {
            return null;
        }
    }
}
?>
