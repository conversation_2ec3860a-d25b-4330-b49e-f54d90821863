<?php
/**
 * 推广控制器
 * 处理推广链接相关的API请求
 */

use Utils\Logger;
use Utils\Helper;
use Services\JDUnionService;

class PromotionController extends BaseController
{
    private $jdService;
    
    public function __construct()
    {
        parent::__construct();
        $this->jdService = new JDUnionService();
    }
    
    /**
     * 生成推广链接
     * POST /api/promotion/link
     */
    public function generateLink()
    {
        try {
            // 验证请求方法
            if ($this->request['method'] !== 'POST') {
                return $this->error('请使用POST方法', 405);
            }
            
            // 获取必需参数
            $materialId = $this->getParam('material_id');
            
            if (empty($materialId)) {
                return $this->error('请提供material_id参数（商品链接或SKU ID）');
            }
            
            // 验证materialId格式
            if (!$this->isValidMaterialId($materialId)) {
                return $this->error('无效的material_id格式');
            }
            
            // 获取可选参数
            $params = [
                'materialId' => $materialId,
                'subUnionId' => $this->getParam('sub_union_id', ''),
                'ext1' => $this->getParam('ext1', ''),
                'couponUrl' => $this->getParam('coupon_url', ''),
                'giftCouponKey' => $this->getParam('gift_coupon_key', ''),
            ];
            
            // 移除空值参数
            $params = array_filter($params, function($value) {
                return $value !== '';
            });
            
            // 调用服务生成推广链接
            $result = $this->jdService->generatePromotionLink($materialId, $params);
            
            // 处理响应数据
            $data = $result['data'] ?? [];
            
            if (empty($data)) {
                return $this->error('生成推广链接失败，请检查商品ID是否正确');
            }
            
            // 格式化响应
            $response = [
                'material_id' => $materialId,
                'promotion_links' => $data,
                'params' => $params,
                'generated_at' => date('Y-m-d H:i:s'),
            ];
            
            Logger::info('生成推广链接成功', [
                'material_id' => $materialId,
                'params' => $params,
                'count' => count($data)
            ]);
            
            return $this->success($response, '生成推广链接成功');
            
        } catch (Exception $e) {
            Logger::error('生成推广链接失败', [
                'material_id' => $materialId ?? '',
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('生成推广链接失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量生成推广链接
     * POST /api/promotion/batch
     */
    public function batchGenerate()
    {
        try {
            // 验证请求方法
            if ($this->request['method'] !== 'POST') {
                return $this->error('请使用POST方法', 405);
            }
            
            // 获取商品ID列表
            $materialIds = $this->getParam('material_ids');
            
            if (empty($materialIds) || !is_array($materialIds)) {
                return $this->error('请提供material_ids数组参数');
            }
            
            if (count($materialIds) > 20) {
                return $this->error('一次最多处理20个商品');
            }
            
            // 验证每个materialId
            foreach ($materialIds as $materialId) {
                if (!$this->isValidMaterialId($materialId)) {
                    return $this->error('无效的material_id格式: ' . $materialId);
                }
            }
            
            // 获取公共参数
            $commonParams = [
                'subUnionId' => $this->getParam('sub_union_id', ''),
                'ext1' => $this->getParam('ext1', ''),
            ];
            
            $results = [];
            $errors = [];
            
            // 逐个生成推广链接
            foreach ($materialIds as $materialId) {
                try {
                    $params = array_merge($commonParams, ['materialId' => $materialId]);
                    $params = array_filter($params, function($value) {
                        return $value !== '';
                    });
                    
                    $result = $this->jdService->generatePromotionLink($materialId, $params);
                    $results[$materialId] = $result['data'] ?? [];
                    
                } catch (Exception $e) {
                    $errors[$materialId] = $e->getMessage();
                    $results[$materialId] = null;
                }
            }
            
            // 统计结果
            $successCount = count(array_filter($results));
            $errorCount = count($errors);
            
            $response = [
                'results' => $results,
                'errors' => $errors,
                'summary' => [
                    'total' => count($materialIds),
                    'success' => $successCount,
                    'failed' => $errorCount,
                ],
                'params' => $commonParams,
                'generated_at' => date('Y-m-d H:i:s'),
            ];
            
            Logger::info('批量生成推广链接', [
                'total' => count($materialIds),
                'success' => $successCount,
                'failed' => $errorCount
            ]);
            
            return $this->success($response, '批量生成推广链接完成');
            
        } catch (Exception $e) {
            Logger::error('批量生成推广链接失败', [
                'material_ids' => $materialIds ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('批量生成推广链接失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取推广链接历史
     * GET /api/promotion/history
     */
    public function history()
    {
        try {
            // 获取查询参数
            $page = intval($this->getParam('page', 1));
            $pageSize = intval($this->getParam('page_size', 20));
            $materialId = $this->getParam('material_id', '');
            $startDate = $this->getParam('start_date', '');
            $endDate = $this->getParam('end_date', '');
            
            // 参数验证
            if ($page < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($pageSize < 1 || $pageSize > 100) {
                return $this->error('每页数量必须在1-100之间');
            }
            
            // 这里应该从数据库查询推广链接历史记录
            // 由于当前没有实现数据库存储，返回示例数据
            $history = [
                [
                    'id' => 1,
                    'material_id' => '100012043978',
                    'promotion_link' => 'https://u.jd.com/example1',
                    'short_url' => 'https://3.cn/abc123',
                    'click_count' => 156,
                    'order_count' => 12,
                    'commission_amount' => 45.60,
                    'created_at' => '2024-07-16 10:30:00',
                    'status' => 'active'
                ],
                [
                    'id' => 2,
                    'material_id' => '100012043979',
                    'promotion_link' => 'https://u.jd.com/example2',
                    'short_url' => 'https://3.cn/def456',
                    'click_count' => 89,
                    'order_count' => 7,
                    'commission_amount' => 23.40,
                    'created_at' => '2024-07-16 09:15:00',
                    'status' => 'active'
                ]
            ];
            
            $response = [
                'list' => $history,
                'pagination' => [
                    'current_page' => $page,
                    'page_size' => $pageSize,
                    'total_count' => count($history),
                    'total_pages' => 1,
                ],
                'filters' => [
                    'material_id' => $materialId,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ],
            ];
            
            return $this->success($response, '获取推广链接历史成功');
            
        } catch (Exception $e) {
            Logger::error('获取推广链接历史失败', ['error' => $e->getMessage()]);
            return $this->error('获取推广链接历史失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 验证materialId格式
     */
    private function isValidMaterialId($materialId)
    {
        // 检查是否为数字（SKU ID）
        if (is_numeric($materialId)) {
            return true;
        }
        
        // 检查是否为京东商品链接
        if (preg_match('/^https?:\/\/(item\.jd\.com|item\.m\.jd\.com)\/\d+\.html/', $materialId)) {
            return true;
        }
        
        // 检查是否为其他有效的京东链接格式
        if (preg_match('/^https?:\/\/.*jd\.com.*/', $materialId)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 从链接中提取SKU ID
     */
    private function extractSkuFromUrl($url)
    {
        if (preg_match('/\/(\d+)\.html/', $url, $matches)) {
            return $matches[1];
        }
        
        if (preg_match('/sku=(\d+)/', $url, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
}
?>
