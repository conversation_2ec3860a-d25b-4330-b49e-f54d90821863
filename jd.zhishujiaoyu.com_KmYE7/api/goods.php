<?php
/**
 * 商品控制器
 * 处理商品相关的API请求
 */

use Utils\Logger;
use Utils\Helper;
use Services\JDUnionService;

class GoodsController extends BaseController
{
    private $jdService;
    
    public function __construct()
    {
        parent::__construct();
        $this->jdService = new JDUnionService();
    }
    
    /**
     * 获取商品列表
     * GET /api/goods
     */
    public function list()
    {
        try {
            // 获取请求参数
            $params = [
                'eliteId' => intval($this->getParam('elite_id', 1)),
                'pageIndex' => intval($this->getParam('page', 1)),
                'pageSize' => intval($this->getParam('page_size', 20)),
                'sortName' => $this->getParam('sort_name', 'price'),
                'sort' => $this->getParam('sort', 'asc'),
            ];
            
            // 参数验证
            if ($params['pageIndex'] < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($params['pageSize'] < 1 || $params['pageSize'] > 100) {
                return $this->error('每页数量必须在1-100之间');
            }
            
            if (!in_array($params['eliteId'], [1, 2, 10, 15, 22, 23])) {
                return $this->error('无效的榜单类型');
            }
            
            if (!in_array($params['sortName'], ['price', 'commissionShare', 'inOrderCount30Days'])) {
                return $this->error('无效的排序字段');
            }
            
            if (!in_array($params['sort'], ['asc', 'desc'])) {
                return $this->error('无效的排序方式');
            }
            
            // 调用服务获取数据
            $result = $this->jdService->getGoodsList($params);
            
            // 格式化响应数据
            $response = [
                'list' => $result['data'] ?? [],
                'pagination' => [
                    'current_page' => $params['pageIndex'],
                    'page_size' => $params['pageSize'],
                    'total_count' => $result['total_count'] ?? 0,
                    'total_pages' => ceil(($result['total_count'] ?? 0) / $params['pageSize']),
                ],
                'params' => $params,
            ];
            
            Logger::info('获取商品列表', [
                'params' => $params,
                'count' => count($response['list'])
            ]);
            
            return $this->success($response, '获取商品列表成功');
            
        } catch (Exception $e) {
            Logger::error('获取商品列表失败', [
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取商品列表失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取商品详情
     * GET /api/goods/detail
     */
    public function detail()
    {
        try {
            // 获取SKU ID参数
            $skuIds = $this->getParam('sku_ids');
            $skuId = $this->getParam('sku_id');
            
            if (empty($skuIds) && empty($skuId)) {
                return $this->error('请提供sku_id或sku_ids参数');
            }
            
            // 处理参数
            if (!empty($skuId)) {
                $skuIds = $skuId;
            }
            
            // 如果是数组，转换为逗号分隔的字符串
            if (is_array($skuIds)) {
                $skuIds = implode(',', $skuIds);
            }
            
            // 验证SKU ID格式
            $skuArray = explode(',', $skuIds);
            foreach ($skuArray as $sku) {
                if (!is_numeric(trim($sku))) {
                    return $this->error('SKU ID必须为数字');
                }
            }
            
            if (count($skuArray) > 20) {
                return $this->error('一次最多查询20个商品');
            }
            
            // 调用服务获取数据
            $result = $this->jdService->getGoodsDetail($skuIds);
            
            Logger::info('获取商品详情', [
                'sku_ids' => $skuIds,
                'count' => count($result['data'] ?? [])
            ]);
            
            return $this->success($result['data'] ?? [], '获取商品详情成功');
            
        } catch (Exception $e) {
            Logger::error('获取商品详情失败', [
                'sku_ids' => $skuIds ?? '',
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取商品详情失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 搜索商品
     * GET /api/goods/search
     */
    public function search()
    {
        try {
            // 获取搜索关键词
            $keyword = trim($this->getParam('keyword', ''));
            
            if (empty($keyword)) {
                return $this->error('请提供搜索关键词');
            }
            
            if (mb_strlen($keyword, 'UTF-8') < 2) {
                return $this->error('搜索关键词至少2个字符');
            }
            
            if (mb_strlen($keyword, 'UTF-8') > 50) {
                return $this->error('搜索关键词不能超过50个字符');
            }
            
            // 获取其他参数
            $params = [
                'keyword' => $keyword,
                'pageIndex' => intval($this->getParam('page', 1)),
                'pageSize' => intval($this->getParam('page_size', 20)),
                'sortName' => $this->getParam('sort_name', 'price'),
                'sort' => $this->getParam('sort', 'asc'),
                'cid1' => intval($this->getParam('cid1', 0)),
                'cid2' => intval($this->getParam('cid2', 0)),
                'cid3' => intval($this->getParam('cid3', 0)),
            ];
            
            // 参数验证
            if ($params['pageIndex'] < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($params['pageSize'] < 1 || $params['pageSize'] > 100) {
                return $this->error('每页数量必须在1-100之间');
            }
            
            // 移除空值参数
            $params = array_filter($params, function($value) {
                return $value !== 0 && $value !== '';
            });
            
            // 调用服务搜索商品
            $result = $this->jdService->searchGoods($keyword, $params);
            
            // 格式化响应数据
            $response = [
                'list' => $result['data'] ?? [],
                'pagination' => [
                    'current_page' => $params['pageIndex'],
                    'page_size' => $params['pageSize'],
                    'total_count' => $result['total_count'] ?? 0,
                    'total_pages' => ceil(($result['total_count'] ?? 0) / $params['pageSize']),
                ],
                'search' => [
                    'keyword' => $keyword,
                    'params' => $params,
                ],
            ];
            
            Logger::info('搜索商品', [
                'keyword' => $keyword,
                'params' => $params,
                'count' => count($response['list'])
            ]);
            
            return $this->success($response, '搜索商品成功');
            
        } catch (Exception $e) {
            Logger::error('搜索商品失败', [
                'keyword' => $keyword ?? '',
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('搜索商品失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取商品分类
     * GET /api/goods/categories
     */
    public function categories()
    {
        try {
            // 这里可以实现分类获取逻辑
            // 由于京东联盟API的分类接口比较复杂，这里先返回常用分类
            $categories = [
                ['cid' => 1315, 'name' => '服饰内衣'],
                ['cid' => 1316, 'name' => '鞋靴'],
                ['cid' => 1318, 'name' => '家居家装'],
                ['cid' => 1319, 'name' => '母婴'],
                ['cid' => 1320, 'name' => '美妆个护'],
                ['cid' => 1322, 'name' => '食品饮料'],
                ['cid' => 1672, 'name' => '数码'],
                ['cid' => 4938, 'name' => '家电'],
                ['cid' => 1620, 'name' => '运动户外'],
                ['cid' => 6994, 'name' => '汽车用品'],
            ];
            
            return $this->success($categories, '获取商品分类成功');
            
        } catch (Exception $e) {
            Logger::error('获取商品分类失败', ['error' => $e->getMessage()]);
            return $this->error('获取商品分类失败: ' . $e->getMessage(), 500);
        }
    }
}
?>
