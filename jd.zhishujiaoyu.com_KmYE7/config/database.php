<?php
/**
 * 数据库配置文件
 * 京东联盟服务数据库连接配置
 */

return [
    // 默认数据库连接
    'default' => 'mysql',
    
    // 数据库连接配置
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', 3306),
            'database' => env('DB_DATABASE', 'jd_union_service'),
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => 'jd_',
            'strict' => true,
            'engine' => 'InnoDB',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_STRINGIFY_FETCHES => false,
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            ],
        ],
        
        'sqlite' => [
            'driver' => 'sqlite',
            'database' => __DIR__ . '/../database/database.sqlite',
            'prefix' => 'jd_',
            'foreign_key_constraints' => true,
        ],
    ],
    
    // 数据库表配置
    'tables' => [
        'goods' => 'jd_goods',                    // 商品表
        'categories' => 'jd_categories',          // 分类表
        'positions' => 'jd_positions',           // 推广位表
        'orders' => 'jd_orders',                 // 订单表
        'promotion_links' => 'jd_promotion_links', // 推广链接表
        'sync_logs' => 'jd_sync_logs',           // 同步日志表
        'api_logs' => 'jd_api_logs',             // API调用日志表
        'admin_users' => 'jd_admin_users',       // 管理员表
        'config' => 'jd_config',                 // 配置表
    ],
    
    // 数据库迁移配置
    'migrations' => [
        'table' => 'jd_migrations',
        'path' => __DIR__ . '/../database/migrations',
    ],
    
    // 数据库备份配置
    'backup' => [
        'enabled' => true,
        'path' => __DIR__ . '/../database/backups',
        'keep_days' => 7,
        'compress' => true,
    ],
];

/**
 * 环境变量获取函数
 */
if (!function_exists('env')) {
    function env($key, $default = null) {
        $value = getenv($key);
        if ($value === false) {
            return $default;
        }
        
        // 处理布尔值
        if (in_array(strtolower($value), ['true', 'false'])) {
            return strtolower($value) === 'true';
        }
        
        // 处理null值
        if (strtolower($value) === 'null') {
            return null;
        }
        
        return $value;
    }
}
?>
