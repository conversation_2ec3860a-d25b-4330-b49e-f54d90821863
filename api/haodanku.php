<?php
/**
 * 好单库API控制器
 * 提供好单库商品数据接口
 */

use Utils\Logger;
use Utils\Helper;
use Services\HaodankuService;

class HaodankuController extends BaseController
{
    private $haodankuService;
    
    public function __construct()
    {
        parent::__construct();
        
        try {
            $this->haodankuService = new HaodankuService();
        } catch (Exception $e) {
            Logger::error('好单库服务初始化失败', ['error' => $e->getMessage()]);
            // 继续执行，在具体方法中处理错误
        }
    }
    
    /**
     * 获取商品列表
     * GET /api/haodanku/goods
     */
    public function goods()
    {
        if (!$this->haodankuService) {
            return $this->error('好单库服务未正确配置', 500);
        }
        
        try {
            // 获取请求参数
            $params = [
                'page' => intval($this->getParam('page', 1)),
                'page_size' => intval($this->getParam('page_size', 20)),
                'sort' => $this->getParam('sort', 'sale_num'),
                'cid' => $this->getParam('cid', ''),
                'subcid' => $this->getParam('subcid', ''),
                'back' => $this->getParam('back', ''),
                'min_id' => $this->getParam('min_id', ''),
            ];
            
            // 参数验证
            if ($params['page'] < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($params['page_size'] < 1 || $params['page_size'] > 100) {
                return $this->error('每页数量必须在1-100之间');
            }
            
            if (!in_array($params['sort'], ['sale_num', 'price', 'coupon_amount', 'commission_rate'])) {
                return $this->error('无效的排序方式');
            }
            
            // 调用服务获取数据
            $result = $this->haodankuService->getGoodsList($params);
            
            // 格式化响应数据
            $response = [
                'list' => $result['data'] ?? [],
                'pagination' => [
                    'current_page' => $result['page'],
                    'page_size' => $result['page_size'],
                    'total_count' => $result['total_count'],
                    'total_pages' => ceil($result['total_count'] / $result['page_size']),
                ],
                'params' => $params,
            ];
            
            Logger::info('获取好单库商品列表成功', [
                'params' => $params,
                'count' => count($response['list'])
            ]);
            
            return $this->success($response, '获取商品列表成功');
            
        } catch (Exception $e) {
            Logger::error('获取好单库商品列表失败', [
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取商品列表失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取商品详情
     * GET /api/haodanku/detail
     */
    public function detail()
    {
        if (!$this->haodankuService) {
            return $this->error('好单库服务未正确配置', 500);
        }
        
        try {
            // 获取商品ID参数
            $itemId = $this->getParam('item_id');
            $id = $this->getParam('id');
            
            if (empty($itemId) && empty($id)) {
                return $this->error('请提供item_id或id参数');
            }
            
            // 处理参数
            if (!empty($id)) {
                $itemId = $id;
            }
            
            // 验证商品ID格式
            if (!is_numeric($itemId)) {
                return $this->error('商品ID必须为数字');
            }
            
            // 调用服务获取数据
            $result = $this->haodankuService->getGoodsDetail($itemId);
            
            Logger::info('获取好单库商品详情成功', [
                'item_id' => $itemId
            ]);
            
            return $this->success($result, '获取商品详情成功');
            
        } catch (Exception $e) {
            Logger::error('获取好单库商品详情失败', [
                'item_id' => $itemId ?? '',
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取商品详情失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 搜索商品
     * GET /api/haodanku/search
     */
    public function search()
    {
        if (!$this->haodankuService) {
            return $this->error('好单库服务未正确配置', 500);
        }
        
        try {
            // 获取搜索关键词
            $keyword = trim($this->getParam('keyword', ''));
            
            if (empty($keyword)) {
                return $this->error('请提供搜索关键词');
            }
            
            if (mb_strlen($keyword, 'UTF-8') < 2) {
                return $this->error('搜索关键词至少2个字符');
            }
            
            if (mb_strlen($keyword, 'UTF-8') > 50) {
                return $this->error('搜索关键词不能超过50个字符');
            }
            
            // 获取其他参数
            $params = [
                'page' => intval($this->getParam('page', 1)),
                'page_size' => intval($this->getParam('page_size', 20)),
                'sort' => $this->getParam('sort', 'sale_num'),
                'cid' => $this->getParam('cid', ''),
                'subcid' => $this->getParam('subcid', ''),
                'back' => $this->getParam('back', ''),
            ];
            
            // 参数验证
            if ($params['page'] < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($params['page_size'] < 1 || $params['page_size'] > 100) {
                return $this->error('每页数量必须在1-100之间');
            }
            
            // 调用服务搜索商品
            $result = $this->haodankuService->searchGoods($keyword, $params);
            
            // 格式化响应数据
            $response = [
                'list' => $result['data'] ?? [],
                'pagination' => [
                    'current_page' => $result['page'],
                    'page_size' => $result['page_size'],
                    'total_count' => $result['total_count'],
                    'total_pages' => ceil($result['total_count'] / $result['page_size']),
                ],
                'search' => [
                    'keyword' => $keyword,
                    'params' => $params,
                ],
            ];
            
            Logger::info('好单库商品搜索成功', [
                'keyword' => $keyword,
                'params' => $params,
                'count' => count($response['list'])
            ]);
            
            return $this->success($response, '搜索商品成功');
            
        } catch (Exception $e) {
            Logger::error('好单库商品搜索失败', [
                'keyword' => $keyword ?? '',
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('搜索商品失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取分类列表
     * GET /api/haodanku/categories
     */
    public function categories()
    {
        if (!$this->haodankuService) {
            return $this->error('好单库服务未正确配置', 500);
        }
        
        try {
            // 调用服务获取分类数据
            $result = $this->haodankuService->getCategories();
            
            Logger::info('获取好单库分类列表成功', [
                'count' => count($result)
            ]);
            
            return $this->success($result, '获取分类列表成功');
            
        } catch (Exception $e) {
            Logger::error('获取好单库分类列表失败', [
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取分类列表失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取京东热销排行榜
     * GET /api/haodanku/jdhotrank
     */
    public function jdhotrank()
    {
        if (!$this->haodankuService) {
            return $this->error('好单库服务未正确配置', 500);
        }

        try {
            // 获取请求参数
            $params = [
                'min_id' => intval($this->getParam('min_id', 1)),
                'cid' => intval($this->getParam('cid', 0)),
            ];

            // 参数验证
            if ($params['min_id'] < 1) {
                return $this->error('min_id必须大于0');
            }

            // 调用服务获取数据
            $result = $this->haodankuService->getJdHotRank($params);

            // 格式化响应数据
            $response = [
                'list' => $result['data'] ?? [],
                'total_count' => $result['total_count'],
                'params' => $params,
            ];

            Logger::info('获取京东热销排行榜成功', [
                'params' => $params,
                'count' => count($response['list'])
            ]);

            return $this->success($response, '获取京东热销排行榜成功');

        } catch (Exception $e) {
            Logger::error('获取京东热销排行榜失败', [
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);

            return $this->error('获取京东热销排行榜失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取超值买返商品
     * GET /api/haodanku/supervalue
     */
    public function supervalue()
    {
        if (!$this->haodankuService) {
            return $this->error('好单库服务未正确配置', 500);
        }
        
        try {
            // 获取请求参数
            $params = [
                'page' => intval($this->getParam('page', 1)),
                'page_size' => intval($this->getParam('page_size', 20)),
                'cid' => $this->getParam('cid', ''),
                'subcid' => $this->getParam('subcid', ''),
            ];
            
            // 参数验证
            if ($params['page'] < 1) {
                return $this->error('页码必须大于0');
            }
            
            if ($params['page_size'] < 1 || $params['page_size'] > 100) {
                return $this->error('每页数量必须在1-100之间');
            }
            
            // 调用服务获取数据
            $result = $this->haodankuService->getSuperValueGoods($params);
            
            // 格式化响应数据
            $response = [
                'list' => $result['data'] ?? [],
                'pagination' => [
                    'current_page' => $result['page'],
                    'page_size' => $result['page_size'],
                    'total_count' => $result['total_count'],
                    'total_pages' => ceil($result['total_count'] / $result['page_size']),
                ],
                'params' => $params,
            ];
            
            Logger::info('获取好单库超值买返商品成功', [
                'params' => $params,
                'count' => count($response['list'])
            ]);
            
            return $this->success($response, '获取超值买返商品成功');
            
        } catch (Exception $e) {
            Logger::error('获取好单库超值买返商品失败', [
                'params' => $params ?? [],
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取超值买返商品失败: ' . $e->getMessage(), 500);
        }
    }
}
?>
