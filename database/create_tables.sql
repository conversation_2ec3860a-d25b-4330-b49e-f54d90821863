-- 京东热销商品表
CREATE TABLE IF NOT EXISTS `jd_hot_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sku_id` varchar(50) NOT NULL COMMENT '京东SKU ID',
  `goods_name` varchar(500) NOT NULL COMMENT '商品名称',
  `goods_name_short` varchar(200) DEFAULT NULL COMMENT '商品短名称',
  `goods_desc` text COMMENT '商品描述',
  `item_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品原价',
  `item_end_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品现价',
  `item_sale` int(11) DEFAULT '0' COMMENT '销量',
  `item_pic` varchar(500) DEFAULT NULL COMMENT '商品主图',
  `cid` varchar(20) DEFAULT '0' COMMENT '分类ID',
  `commission_share` decimal(5,2) DEFAULT '0.00' COMMENT '佣金比例',
  `commission` decimal(10,2) DEFAULT '0.00' COMMENT '佣金金额',
  `coupon_url` varchar(1000) DEFAULT NULL COMMENT '优惠券链接',
  `coupon_money` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券金额',
  `coupon_num` int(11) DEFAULT '0' COMMENT '优惠券数量',
  `coupon_start_time` int(11) DEFAULT '0' COMMENT '优惠券开始时间',
  `coupon_end_time` int(11) DEFAULT '0' COMMENT '优惠券结束时间',
  `jd_images` text COMMENT '商品图片集合，JSON格式',
  `shop_name` varchar(200) DEFAULT NULL COMMENT '店铺名称',
  `item_id` varchar(100) DEFAULT NULL COMMENT '好单库商品ID',
  `rank_position` int(11) DEFAULT '0' COMMENT '排行榜位置',
  `sync_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '同步时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=正常，0=下架',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_id` (`sku_id`),
  KEY `idx_rank_position` (`rank_position`),
  KEY `idx_sync_time` (`sync_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='京东热销商品表';

-- 推广链接记录表
CREATE TABLE IF NOT EXISTS `promotion_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sku_id` varchar(50) NOT NULL COMMENT '京东SKU ID',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户ID（可选）',
  `promotion_url` varchar(1000) NOT NULL COMMENT '推广链接',
  `short_url` varchar(500) DEFAULT NULL COMMENT '短链接',
  `click_count` int(11) DEFAULT '0' COMMENT '点击次数',
  `order_count` int(11) DEFAULT '0' COMMENT '订单数量',
  `commission_earned` decimal(10,2) DEFAULT '0.00' COMMENT '已获得佣金',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_click_time` timestamp NULL DEFAULT NULL COMMENT '最后点击时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=有效，0=失效',
  PRIMARY KEY (`id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广链接记录表';

-- 数据同步日志表
CREATE TABLE IF NOT EXISTS `sync_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sync_type` varchar(50) NOT NULL COMMENT '同步类型：jd_hot_rank',
  `sync_status` varchar(20) NOT NULL COMMENT '同步状态：success, failed, running',
  `total_count` int(11) DEFAULT '0' COMMENT '总数量',
  `success_count` int(11) DEFAULT '0' COMMENT '成功数量',
  `failed_count` int(11) DEFAULT '0' COMMENT '失败数量',
  `error_message` text COMMENT '错误信息',
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) DEFAULT '0' COMMENT '耗时（秒）',
  PRIMARY KEY (`id`),
  KEY `idx_sync_type` (`sync_type`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据同步日志表';
