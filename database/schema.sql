-- 京东联盟服务数据库结构
-- 创建时间: 2024-07-16
-- 版本: 1.0.0

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 数据库创建
-- ----------------------------
CREATE DATABASE IF NOT EXISTS `jd_union_service` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `jd_union_service`;

-- ----------------------------
-- 商品表
-- ----------------------------
DROP TABLE IF EXISTS `jd_goods`;
CREATE TABLE `jd_goods` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sku_id` bigint(20) NOT NULL COMMENT '商品SKU ID',
  `sku_name` varchar(500) NOT NULL COMMENT '商品名称',
  `category_info` json DEFAULT NULL COMMENT '分类信息',
  `image_info` json DEFAULT NULL COMMENT '图片信息',
  `price_info` json DEFAULT NULL COMMENT '价格信息',
  `commission_info` json DEFAULT NULL COMMENT '佣金信息',
  `shop_info` json DEFAULT NULL COMMENT '店铺信息',
  `coupon_info` json DEFAULT NULL COMMENT '优惠券信息',
  `promotion_info` json DEFAULT NULL COMMENT '推广信息',
  `brand_code` varchar(100) DEFAULT NULL COMMENT '品牌编码',
  `brand_name` varchar(200) DEFAULT NULL COMMENT '品牌名称',
  `owner` varchar(50) DEFAULT NULL COMMENT '商品类型：g=自营，p=pop',
  `pin_gou_info` json DEFAULT NULL COMMENT '拼购信息',
  `resource_info` json DEFAULT NULL COMMENT '资源信息',
  `in_order_count_30_days` int(11) DEFAULT 0 COMMENT '30天引单数量',
  `in_order_count_30_days_sku` int(11) DEFAULT 0 COMMENT '30天支付订单量',
  `material_url` text COMMENT '商品落地页',
  `sale_tips` varchar(200) DEFAULT NULL COMMENT '销售文案',
  `skuid_link` varchar(500) DEFAULT NULL COMMENT '商品链接',
  `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否爆款',
  `spuid` bigint(20) DEFAULT NULL COMMENT 'SPUID',
  `brand_id` int(11) DEFAULT NULL COMMENT '品牌ID',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺ID',
  `has_content` tinyint(1) DEFAULT 0 COMMENT '是否有内容',
  `has_best_coupon` tinyint(1) DEFAULT 0 COMMENT '是否有最优优惠券',
  `pid` varchar(100) DEFAULT NULL COMMENT '推广位ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=正常，0=下架',
  `sync_time` timestamp NULL DEFAULT NULL COMMENT '同步时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_id` (`sku_id`),
  KEY `idx_brand_code` (`brand_code`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sync_time` (`sync_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='京东商品表';

-- ----------------------------
-- 分类表
-- ----------------------------
DROP TABLE IF EXISTS `jd_categories`;
CREATE TABLE `jd_categories` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cid1` int(11) NOT NULL COMMENT '一级分类ID',
  `cid1_name` varchar(100) NOT NULL COMMENT '一级分类名称',
  `cid2` int(11) DEFAULT NULL COMMENT '二级分类ID',
  `cid2_name` varchar(100) DEFAULT NULL COMMENT '二级分类名称',
  `cid3` int(11) DEFAULT NULL COMMENT '三级分类ID',
  `cid3_name` varchar(100) DEFAULT NULL COMMENT '三级分类名称',
  `parent_id` int(11) DEFAULT 0 COMMENT '父分类ID',
  `level` tinyint(1) DEFAULT 1 COMMENT '分类级别：1,2,3',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `goods_count` int(11) DEFAULT 0 COMMENT '商品数量',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_cid1` (`cid1`),
  KEY `idx_cid2` (`cid2`),
  KEY `idx_cid3` (`cid3`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='京东分类表';

-- ----------------------------
-- 推广位表
-- ----------------------------
DROP TABLE IF EXISTS `jd_positions`;
CREATE TABLE `jd_positions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `position_id` bigint(20) NOT NULL COMMENT '推广位ID',
  `site_id` bigint(20) NOT NULL COMMENT '网站ID',
  `position_name` varchar(200) NOT NULL COMMENT '推广位名称',
  `union_id` bigint(20) DEFAULT NULL COMMENT '联盟ID',
  `type` tinyint(1) DEFAULT 1 COMMENT '推广位类型：1=网站，2=APP，3=导购媒体，4=聊天工具',
  `space_name` varchar(200) DEFAULT NULL COMMENT '站点名称',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=正常，0=禁用',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_position_id` (`position_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='京东推广位表';

-- ----------------------------
-- 推广链接表
-- ----------------------------
DROP TABLE IF EXISTS `jd_promotion_links`;
CREATE TABLE `jd_promotion_links` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sku_id` bigint(20) NOT NULL COMMENT '商品SKU ID',
  `position_id` bigint(20) NOT NULL COMMENT '推广位ID',
  `material_id` varchar(500) NOT NULL COMMENT '推广素材ID（商品链接或ID）',
  `union_id` bigint(20) DEFAULT NULL COMMENT '联盟ID',
  `sub_union_id` varchar(100) DEFAULT NULL COMMENT '子渠道标识',
  `short_url` varchar(500) DEFAULT NULL COMMENT '短链接',
  `click_url` varchar(1000) DEFAULT NULL COMMENT '点击链接',
  `jd_click_url` varchar(1000) DEFAULT NULL COMMENT '京东点击链接',
  `coupon_url` varchar(1000) DEFAULT NULL COMMENT '优惠券链接',
  `gift_coupon_key` varchar(100) DEFAULT NULL COMMENT '礼金批次号',
  `ext1` varchar(200) DEFAULT NULL COMMENT '扩展参数1',
  `ext2` varchar(200) DEFAULT NULL COMMENT '扩展参数2',
  `ext3` varchar(200) DEFAULT NULL COMMENT '扩展参数3',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '链接过期时间',
  `click_count` int(11) DEFAULT 0 COMMENT '点击次数',
  `order_count` int(11) DEFAULT 0 COMMENT '订单数量',
  `commission_amount` decimal(10,2) DEFAULT 0.00 COMMENT '佣金金额',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=正常，0=失效',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_position_id` (`position_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_sub_union_id` (`sub_union_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='京东推广链接表';

-- ----------------------------
-- 订单表
-- ----------------------------
DROP TABLE IF EXISTS `jd_orders`;
CREATE TABLE `jd_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父单的订单ID',
  `order_time` timestamp NULL DEFAULT NULL COMMENT '下单时间',
  `finish_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `modify_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `order_emt` tinyint(1) DEFAULT NULL COMMENT '下单设备类型：1=PC，2=无线',
  `plus` tinyint(1) DEFAULT 0 COMMENT '是否为PLUS会员订单',
  `union_id` bigint(20) DEFAULT NULL COMMENT '联盟ID',
  `sku_info` json DEFAULT NULL COMMENT 'SKU信息',
  `price_info` json DEFAULT NULL COMMENT '价格信息',
  `commission_info` json DEFAULT NULL COMMENT '佣金信息',
  `position_index` int(11) DEFAULT NULL COMMENT '推广位',
  `sub_union_id` varchar(100) DEFAULT NULL COMMENT '子渠道标识',
  `trace_type` tinyint(1) DEFAULT NULL COMMENT '订单跟踪类型',
  `pay_month` varchar(10) DEFAULT NULL COMMENT '订单维度预估结算时间',
  `cpact_id` bigint(20) DEFAULT NULL COMMENT '招商团活动ID',
  `union_alias` varchar(100) DEFAULT NULL COMMENT '联盟标签数据',
  `pid` varchar(100) DEFAULT NULL COMMENT '推广位ID',
  `site_id` bigint(20) DEFAULT NULL COMMENT '网站ID',
  `union_role` tinyint(1) DEFAULT NULL COMMENT '联盟角色',
  `gift_coupon_ocs_amount` decimal(10,2) DEFAULT 0.00 COMMENT '礼金分摊金额',
  `gift_coupon_key` varchar(100) DEFAULT NULL COMMENT '礼金批次号',
  `balance_ext` varchar(500) DEFAULT NULL COMMENT '计佣扩展信息',
  `order_status` tinyint(1) DEFAULT 1 COMMENT '订单状态：1=已付款，2=已完成，3=已取消',
  `valid_code` tinyint(1) DEFAULT 1 COMMENT '有效码：1=有效，0=无效',
  `sync_time` timestamp NULL DEFAULT NULL COMMENT '同步时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_position_index` (`position_index`),
  KEY `idx_sub_union_id` (`sub_union_id`),
  KEY `idx_order_time` (`order_time`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='京东订单表';

-- ----------------------------
-- 同步日志表
-- ----------------------------
DROP TABLE IF EXISTS `jd_sync_logs`;
CREATE TABLE `jd_sync_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sync_type` varchar(50) NOT NULL COMMENT '同步类型：goods,orders,categories',
  `sync_method` varchar(50) NOT NULL COMMENT '同步方法：full,incremental',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `total_count` int(11) DEFAULT 0 COMMENT '总数量',
  `success_count` int(11) DEFAULT 0 COMMENT '成功数量',
  `failed_count` int(11) DEFAULT 0 COMMENT '失败数量',
  `error_message` text COMMENT '错误信息',
  `sync_params` json DEFAULT NULL COMMENT '同步参数',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0=进行中，1=成功，2=失败',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sync_type` (`sync_type`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='同步日志表';

-- ----------------------------
-- API调用日志表
-- ----------------------------
DROP TABLE IF EXISTS `jd_api_logs`;
CREATE TABLE `jd_api_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_method` varchar(100) NOT NULL COMMENT 'API方法名',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_params` json DEFAULT NULL COMMENT '请求参数',
  `response_data` json DEFAULT NULL COMMENT '响应数据',
  `response_code` int(11) DEFAULT NULL COMMENT '响应状态码',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间（毫秒）',
  `error_message` text COMMENT '错误信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=成功，0=失败',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_method` (`api_method`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API调用日志表';

-- ----------------------------
-- 管理员表
-- ----------------------------
DROP TABLE IF EXISTS `jd_admin_users`;
CREATE TABLE `jd_admin_users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `role` varchar(20) DEFAULT 'admin' COMMENT '角色：admin,operator',
  `permissions` json DEFAULT NULL COMMENT '权限列表',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) DEFAULT 0 COMMENT '登录次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=正常，0=禁用',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- ----------------------------
-- 配置表
-- ----------------------------
DROP TABLE IF EXISTS `jd_config`;
CREATE TABLE `jd_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型：string,int,bool,json',
  `config_group` varchar(50) DEFAULT 'system' COMMENT '配置分组',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统配置',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`),
  KEY `idx_is_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置表';

-- ----------------------------
-- 初始化数据
-- ----------------------------

-- 插入默认管理员
INSERT INTO `jd_admin_users` (`username`, `password`, `email`, `real_name`, `role`, `status`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '系统管理员', 'admin', 1);

-- 插入默认配置
INSERT INTO `jd_config` (`config_key`, `config_value`, `config_type`, `config_group`, `description`, `is_system`) VALUES
('site_name', '京东联盟服务', 'string', 'system', '网站名称', 1),
('site_version', '1.0.0', 'string', 'system', '系统版本', 1),
('sync_enabled', '1', 'bool', 'sync', '是否启用数据同步', 0),
('sync_interval', '3600', 'int', 'sync', '同步间隔（秒）', 0),
('cache_enabled', '1', 'bool', 'cache', '是否启用缓存', 0),
('log_level', 'info', 'string', 'log', '日志级别', 0);

SET FOREIGN_KEY_CHECKS = 1;
