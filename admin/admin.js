/**
 * 京东联盟服务管理后台JavaScript
 */

// 全局变量
let currentPage = 'dashboard';
let apiBaseUrl = '../';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initAdmin();
});

/**
 * 初始化管理后台
 */
function initAdmin() {
    console.log('管理后台初始化...');
    
    // 加载仪表盘数据
    loadDashboard();
    
    // 设置定时刷新
    setInterval(function() {
        if (currentPage === 'dashboard') {
            refreshDashboard();
        }
    }, 30000); // 30秒刷新一次
}

/**
 * 显示指定页面
 */
function showPage(pageName) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page-content');
    pages.forEach(page => {
        page.style.display = 'none';
    });
    
    // 显示指定页面
    const targetPage = document.getElementById(pageName + '-page');
    if (targetPage) {
        targetPage.style.display = 'block';
        currentPage = pageName;
    }
    
    // 更新导航状态
    updateNavigation(pageName);
    
    // 加载页面数据
    loadPageData(pageName);
}

/**
 * 更新导航状态
 */
function updateNavigation(activePage) {
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // 找到对应的导航链接并激活
    const activeLink = document.querySelector(`.sidebar .nav-link[onclick*="${activePage}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

/**
 * 加载页面数据
 */
function loadPageData(pageName) {
    switch (pageName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'config':
            loadConfig();
            break;
        case 'goods':
            loadGoods();
            break;
        case 'promotion':
            loadPromotion();
            break;
        case 'logs':
            loadLogs();
            break;
        case 'system':
            loadSystem();
            break;
    }
}

/**
 * 加载仪表盘数据
 */
function loadDashboard() {
    console.log('加载仪表盘数据...');
    
    // 显示加载状态
    showLoading('system-info');
    
    // 获取系统状态
    fetch(apiBaseUrl + 'api/status')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                updateDashboardStats(data.data);
                displaySystemInfo(data.data);
            } else {
                showError('获取系统状态失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('获取系统状态失败:', error);
            showError('获取系统状态失败: ' + error.message);
        })
        .finally(() => {
            hideLoading('system-info');
        });
}

/**
 * 更新仪表盘统计数据
 */
function updateDashboardStats(data) {
    // 更新系统状态
    const systemStatus = document.getElementById('system-status');
    if (systemStatus) {
        systemStatus.textContent = data.status === 'running' ? '运行中' : '异常';
        systemStatus.className = data.status === 'running' ? 'h2 font-weight-bold mb-0 text-success' : 'h2 font-weight-bold mb-0 text-danger';
    }
    
    // 更新内存使用率
    const memoryUsage = document.getElementById('memory-usage');
    if (memoryUsage && data.resources) {
        const usage = data.resources.memory_usage || 0;
        const limit = data.resources.memory_limit || 1;
        const percentage = Math.round((usage / limit) * 100);
        memoryUsage.textContent = percentage + '%';
    }
    
    // 更新API调用次数（示例数据）
    const apiCalls = document.getElementById('api-calls');
    if (apiCalls) {
        apiCalls.textContent = Math.floor(Math.random() * 1000);
    }
    
    // 更新缓存命中率（示例数据）
    const cacheHitRate = document.getElementById('cache-hit-rate');
    if (cacheHitRate) {
        cacheHitRate.textContent = (90 + Math.floor(Math.random() * 10)) + '%';
    }
}

/**
 * 显示系统信息
 */
function displaySystemInfo(data) {
    const systemInfoDiv = document.getElementById('system-info');
    if (!systemInfoDiv) return;
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted">基本信息</h6>
                <ul class="list-unstyled">
                    <li><strong>服务名称:</strong> ${data.service || 'N/A'}</li>
                    <li><strong>版本:</strong> ${data.version || 'N/A'}</li>
                    <li><strong>状态:</strong> <span class="badge bg-success">${data.status || 'N/A'}</span></li>
                    <li><strong>运行时间:</strong> ${formatUptime(data.uptime)}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6 class="text-muted">配置信息</h6>
                <ul class="list-unstyled">
                    <li><strong>调试模式:</strong> ${data.config?.debug ? '开启' : '关闭'}</li>
                    <li><strong>时区:</strong> ${data.config?.timezone || 'N/A'}</li>
                    <li><strong>日志级别:</strong> ${data.config?.log_level || 'N/A'}</li>
                    <li><strong>缓存驱动:</strong> ${data.config?.cache_driver || 'N/A'}</li>
                </ul>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-4">
                <h6 class="text-muted">数据库</h6>
                <span class="badge ${data.services?.database?.status === 'ok' ? 'bg-success' : 'bg-danger'}">
                    ${data.services?.database?.status === 'ok' ? '正常' : '异常'}
                </span>
            </div>
            <div class="col-md-4">
                <h6 class="text-muted">缓存</h6>
                <span class="badge ${data.services?.cache?.status === 'ok' ? 'bg-success' : 'bg-danger'}">
                    ${data.services?.cache?.status === 'ok' ? '正常' : '异常'}
                </span>
            </div>
            <div class="col-md-4">
                <h6 class="text-muted">京东联盟</h6>
                <span class="badge ${data.services?.jd_union?.status === 'configured' ? 'bg-success' : 'bg-warning'}">
                    ${data.services?.jd_union?.status === 'configured' ? '已配置' : '未配置'}
                </span>
            </div>
        </div>
    `;
    
    systemInfoDiv.innerHTML = html;
}

/**
 * 格式化运行时间
 */
function formatUptime(seconds) {
    if (!seconds) return 'N/A';
    
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
        return `${days}天 ${hours}小时 ${minutes}分钟`;
    } else if (hours > 0) {
        return `${hours}小时 ${minutes}分钟`;
    } else {
        return `${minutes}分钟`;
    }
}

/**
 * 刷新仪表盘
 */
function refreshDashboard() {
    console.log('刷新仪表盘数据...');
    loadDashboard();
}

/**
 * 测试API连接
 */
function testApi() {
    showAlert('正在测试API连接...', 'info');
    
    fetch(apiBaseUrl + 'api/test')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showAlert('API连接测试成功！', 'success');
            } else {
                showAlert('API连接测试失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('API测试失败:', error);
            showAlert('API连接测试失败: ' + error.message, 'danger');
        });
}

/**
 * 清理缓存
 */
function clearCache() {
    if (!confirm('确定要清理所有缓存吗？')) {
        return;
    }
    
    showAlert('正在清理缓存...', 'info');
    
    // 这里应该调用清理缓存的API
    setTimeout(() => {
        showAlert('缓存清理完成！', 'success');
    }, 1000);
}

/**
 * 退出登录
 */
function logout() {
    if (confirm('确定要退出登录吗？')) {
        window.location.href = 'login.html';
    }
}

/**
 * 显示加载状态
 */
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'block';
        element.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载...</p>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'none';
    }
}

/**
 * 显示错误信息
 */
function showError(message) {
    showAlert(message, 'danger');
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

/**
 * 加载配置页面
 */
function loadConfig() {
    console.log('加载配置页面...');
    // 配置页面功能待实现
}

/**
 * 加载商品页面
 */
function loadGoods() {
    console.log('加载商品页面...');
    // 商品页面功能待实现
}

/**
 * 加载推广页面
 */
function loadPromotion() {
    console.log('加载推广页面...');
    // 推广页面功能待实现
}

/**
 * 加载日志页面
 */
function loadLogs() {
    console.log('加载日志页面...');
    // 日志页面功能待实现
}

/**
 * 加载系统页面
 */
function loadSystem() {
    console.log('加载系统页面...');
    // 系统页面功能待实现
}
