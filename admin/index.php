<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东联盟服务管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
        }
        .sidebar .nav-link:hover {
            color: #fff;
            background: #495057;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background: #0d6efd;
        }
        .main-content {
            padding: 2rem;
        }
        .card-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card-stats .card-body {
            padding: 1.5rem;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .table-responsive {
            border-radius: 0.375rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shop"></i>
                京东联盟服务管理后台
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    退出登录
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showPage('dashboard')">
                                <i class="bi bi-speedometer2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('config')">
                                <i class="bi bi-gear"></i>
                                配置管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('goods')">
                                <i class="bi bi-box"></i>
                                商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('promotion')">
                                <i class="bi bi-link-45deg"></i>
                                推广链接
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('logs')">
                                <i class="bi bi-file-text"></i>
                                日志查看
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('system')">
                                <i class="bi bi-cpu"></i>
                                系统状态
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 仪表盘页面 -->
                <div id="dashboard-page" class="page-content">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">仪表盘</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                                <i class="bi bi-arrow-clockwise"></i>
                                刷新
                            </button>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card card-stats border-0">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <h5 class="card-title text-uppercase text-muted mb-0">系统状态</h5>
                                            <span class="h2 font-weight-bold mb-0" id="system-status">运行中</span>
                                        </div>
                                        <div class="col-auto">
                                            <div class="icon icon-shape bg-white text-primary rounded-circle shadow">
                                                <i class="bi bi-cpu" style="font-size: 1.5rem;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card card-stats border-0">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <h5 class="card-title text-uppercase text-muted mb-0">API调用</h5>
                                            <span class="h2 font-weight-bold mb-0" id="api-calls">0</span>
                                        </div>
                                        <div class="col-auto">
                                            <div class="icon icon-shape bg-white text-success rounded-circle shadow">
                                                <i class="bi bi-graph-up" style="font-size: 1.5rem;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card card-stats border-0">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <h5 class="card-title text-uppercase text-muted mb-0">缓存命中率</h5>
                                            <span class="h2 font-weight-bold mb-0" id="cache-hit-rate">95%</span>
                                        </div>
                                        <div class="col-auto">
                                            <div class="icon icon-shape bg-white text-info rounded-circle shadow">
                                                <i class="bi bi-lightning" style="font-size: 1.5rem;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card card-stats border-0">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <h5 class="card-title text-uppercase text-muted mb-0">内存使用</h5>
                                            <span class="h2 font-weight-bold mb-0" id="memory-usage">45%</span>
                                        </div>
                                        <div class="col-auto">
                                            <div class="icon icon-shape bg-white text-warning rounded-circle shadow">
                                                <i class="bi bi-memory" style="font-size: 1.5rem;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">系统信息</h5>
                                </div>
                                <div class="card-body">
                                    <div id="system-info" class="loading">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载系统信息...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">快速操作</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" onclick="testApi()">
                                            <i class="bi bi-play-circle"></i>
                                            测试API连接
                                        </button>
                                        <button class="btn btn-success" onclick="clearCache()">
                                            <i class="bi bi-trash"></i>
                                            清理缓存
                                        </button>
                                        <button class="btn btn-info" onclick="showPage('config')">
                                            <i class="bi bi-gear"></i>
                                            配置管理
                                        </button>
                                        <button class="btn btn-warning" onclick="showPage('logs')">
                                            <i class="bi bi-file-text"></i>
                                            查看日志
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他页面内容将通过JavaScript动态加载 -->
                <div id="config-page" class="page-content" style="display: none;">
                    <h2>配置管理</h2>
                    <p>配置管理功能开发中...</p>
                </div>

                <div id="goods-page" class="page-content" style="display: none;">
                    <h2>商品管理</h2>
                    <p>商品管理功能开发中...</p>
                </div>

                <div id="promotion-page" class="page-content" style="display: none;">
                    <h2>推广链接</h2>
                    <p>推广链接管理功能开发中...</p>
                </div>

                <div id="logs-page" class="page-content" style="display: none;">
                    <h2>日志查看</h2>
                    <p>日志查看功能开发中...</p>
                </div>

                <div id="system-page" class="page-content" style="display: none;">
                    <h2>系统状态</h2>
                    <p>系统状态监控功能开发中...</p>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="admin.js"></script>
</body>
</html>
