<!-- 商品管理页面 -->
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h2>商品管理</h2>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#searchModal">
                    <i class="bi bi-search"></i> 高级搜索
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportGoods()">
                    <i class="bi bi-download"></i> 导出
                </button>
            </div>
            <button type="button" class="btn btn-sm btn-primary" onclick="syncGoods()">
                <i class="bi bi-arrow-repeat"></i> 同步商品
            </button>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="input-group">
                <input type="text" id="searchInput" class="form-control" placeholder="搜索商品名称、SKU...">
                <button class="btn btn-outline-secondary" type="button" onclick="searchGoods()">
                    <i class="bi bi-search"></i> 搜索
                </button>
            </div>
        </div>
    </div>

    <!-- 商品列表 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">商品列表</h5>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="showOnlyInStock" onchange="filterGoods()">
                <label class="form-check-label" for="showOnlyInStock">仅显示有货</label>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th width="60">图片</th>
                            <th>商品名称</th>
                            <th>价格</th>
                            <th>佣金</th>
                            <th>销量</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="goodsList">
                        <!-- 商品列表将通过JavaScript动态加载 -->
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2">正在加载商品数据...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 高级搜索模态框 -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">高级搜索</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="advancedSearchForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="keyword" class="form-label">关键词</label>
                            <input type="text" class="form-control" id="keyword" name="keyword" placeholder="商品名称、关键词">
                        </div>
                        <div class="col-md-6">
                            <label for="category" class="form-label">商品类目</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">全部类目</option>
                                <option value="1">手机数码</option>
                                <option value="2">电脑办公</option>
                                <!-- 其他类目将通过JavaScript动态加载 -->
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="minPrice" class="form-label">最低价</label>
                            <input type="number" class="form-control" id="minPrice" name="minPrice" placeholder="0">
                        </div>
                        <div class="col-md-4">
                            <label for="maxPrice" class="form-label">最高价</label>
                            <input type="number" class="form-control" id="maxPrice" name="maxPrice" placeholder="不限">
                        </div>
                        <div class="col-md-4">
                            <label for="sortBy" class="form-label">排序方式</label>
                            <select class="form-select" id="sortBy" name="sortBy">
                                <option value="price_asc">价格从低到高</option>
                                <option value="price_desc">价格从高到低</option>
                                <option value="sale_desc">销量从高到低</option>
                                <option value="commission_desc">佣金比例从高到低</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="inStockOnly" name="inStockOnly">
                        <label class="form-check-label" for="inStockOnly">仅显示有货</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="applyAdvancedSearch()">搜索</button>
            </div>
        </div>
    </div>
</div>

<!-- 商品详情模态框 -->
<div class="modal fade" id="goodsDetailModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="goodsDetailTitle">商品详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="goodsDetailContent">
                <!-- 商品详情将通过JavaScript动态加载 -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="generatePromoBtn" onclick="generatePromoLink()">
                    <i class="bi bi-link-45deg"></i> 生成推广链接
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 商品数据
let currentPageNum = 1;
const pageSize = 10;
let totalGoods = 0;
let currentGoodsList = [];

// 页面加载时获取商品列表
function loadGoodsPage() {
    // 显示加载中
    document.getElementById('goodsList').innerHTML = `
        <tr>
            <td colspan="7" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载商品数据...</div>
            </td>
        </tr>
    `;
    
    // 模拟API请求
    setTimeout(() => {
        // 模拟商品数据
        currentGoodsList = [];
        for (let i = 1; i <= 50; i++) {
            currentGoodsList.push({
                id: 10000 + i,
                name: `测试商品 ${i} 京东自营 正品保证 全国联保`,
                price: (Math.random() * 1000 + 10).toFixed(2),
                commission: (Math.random() * 50 + 5).toFixed(2),
                commissionRate: (Math.random() * 10 + 1).toFixed(1),
                sales: Math.floor(Math.random() * 10000),
                image: 'https://img10.360buyimg.com/n1/s200x200_jfs/t1/123123/12/12345/123456/5f8a1b2eE12345678/abcdef123456.jpg',
                inStock: Math.random() > 0.2,
                category: Math.floor(Math.random() * 5) + 1
            });
        }
        
        totalGoods = currentGoodsList.length;
        renderGoodsList();
        renderPagination();
    }, 500);
}

// 渲染商品列表
function renderGoodsList() {
    const start = (currentPageNum - 1) * pageSize;
    const end = start + pageSize;
    const goodsToShow = currentGoodsList.slice(start, end);
    
    if (goodsToShow.length === 0) {
        document.getElementById('goodsList').innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-5 text-muted">
                    <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                    <div class="mt-2">没有找到符合条件的商品</div>
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    goodsToShow.forEach(goods => {
        html += `
            <tr>
                <td>
                    <img src="${goods.image}" alt="${goods.name}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 300px;" title="${goods.name}">
                        ${goods.name}
                    </div>
                    <div class="text-muted small">SKU: ${goods.id}</div>
                </td>
                <td class="text-nowrap text-danger fw-bold">¥${goods.price}</td>
                <td class="text-nowrap">
                    <span class="text-success">¥${goods.commission}</span>
                    <small class="text-muted d-block">${goods.commissionRate}%</small>
                </td>
                <td>${goods.sales.toLocaleString()}</td>
                <td>
                    <span class="badge ${goods.inStock ? 'bg-success' : 'bg-secondary'}">
                        ${goods.inStock ? '有货' : '无货'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewGoodsDetail(${goods.id})">
                        <i class="bi bi-eye"></i> 查看
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="generatePromoLink(${goods.id}, '${goods.name}')">
                        <i class="bi bi-link-45deg"></i> 推广
                    </button>
                </td>
            </tr>
        `;
    });
    
    document.getElementById('goodsList').innerHTML = html;
}

// 渲染分页
function renderPagination() {
    const totalPages = Math.ceil(totalGoods / pageSize);
    let html = '';
    
    // 上一页按钮
    html += `
        <li class="page-item ${currentPageNum === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPageNum - 1})" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPageNum - 2 && i <= currentPageNum + 2)) {
            html += `
                <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPageNum - 3 || i === currentPageNum + 3) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // 下一页按钮
    html += `
        <li class="page-item ${currentPageNum === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPageNum + 1})" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;
    
    document.getElementById('pagination').innerHTML = html;
}

// 切换页码
function changePage(pageNum) {
    if (pageNum < 1 || pageNum > Math.ceil(totalGoods / pageSize)) return;
    currentPageNum = pageNum;
    renderGoodsList();
    renderPagination();
    window.scrollTo(0, 0);
}

// 搜索商品
function searchGoods() {
    const keyword = document.getElementById('searchInput').value.trim().toLowerCase();
    if (!keyword) {
        currentPageNum = 1;
        renderGoodsList();
        renderPagination();
        return;
    }
    
    currentGoodsList = currentGoodsList.filter(goods => 
        goods.name.toLowerCase().includes(keyword) || 
        goods.id.toString().includes(keyword)
    );
    
    totalGoods = currentGoodsList.length;
    currentPageNum = 1;
    renderGoodsList();
    renderPagination();
}

// 筛选商品（仅显示有货）
function filterGoods() {
    const showOnlyInStock = document.getElementById('showOnlyInStock').checked;
    if (!showOnlyInStock) {
        currentGoodsList = []; // 重新加载所有商品
        loadGoodsPage();
        return;
    }
    
    currentGoodsList = currentGoodsList.filter(goods => goods.inStock);
    totalGoods = currentGoodsList.length;
    currentPageNum = 1;
    renderGoodsList();
    renderPagination();
}

// 查看商品详情
function viewGoodsDetail(goodsId) {
    const goods = currentGoodsList.find(item => item.id === goodsId);
    if (!goods) return;
    
    const modal = new bootstrap.Modal(document.getElementById('goodsDetailModal'));
    document.getElementById('goodsDetailTitle').textContent = goods.name;
    
    // 模拟加载商品详情
    document.getElementById('goodsDetailContent').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <img src="${goods.image}" class="img-fluid rounded" alt="${goods.name}">
            </div>
            <div class="col-md-6">
                <h4>${goods.name}</h4>
                <div class="mb-3">
                    <span class="h3 text-danger fw-bold">¥${goods.price}</span>
                    <span class="text-muted text-decoration-line-through ms-2">¥${(goods.price * 1.2).toFixed(2)}</span>
                    <span class="badge bg-danger ms-2">限时折扣</span>
                </div>
                <div class="mb-3">
                    <span class="text-muted">佣金：</span>
                    <span class="text-success fw-bold">¥${goods.commission} (${goods.commissionRate}%)</span>
                </div>
                <div class="mb-3">
                    <span class="text-muted">已售：</span>
                    <span>${goods.sales.toLocaleString()}件</span>
                </div>
                <div class="mb-3">
                    <span class="text-muted">库存：</span>
                    <span class="badge ${goods.inStock ? 'bg-success' : 'bg-secondary'}">
                        ${goods.inStock ? '有货' : '无货'}
                    </span>
                </div>
                <div class="d-grid gap-2 d-md-flex">
                    <button class="btn btn-primary btn-lg" onclick="generatePromoLink(${goods.id}, '${goods.name}')">
                        <i class="bi bi-cart-plus"></i> 立即推广
                    </button>
                    <button class="btn btn-outline-secondary btn-lg">
                        <i class="bi bi-heart"></i> 收藏
                    </button>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <h5>商品详情</h5>
                <div class="border rounded p-3 bg-light">
                    <p>这里是商品详细描述内容，包含商品参数、规格、包装清单等信息。</p>
                    <p>商品编号：${goods.id}</p>
                    <p>商品毛重：1.2kg</p>
                    <p>商品产地：中国大陆</p>
                    <p>类别：数码产品</p>
                    <p>品牌：测试品牌</p>
                </div>
            </div>
        </div>
    `;
    
    modal.show();
}

// 生成推广链接
function generatePromoLink(goodsId, goodsName) {
    const goods = currentGoodsList.find(item => item.id === goodsId);
    if (!goods) {
        showAlert('未找到商品信息', 'danger');
        return;
    }
    
    // 模拟生成推广链接
    const promoLink = `https://u.jd.com/abc123?sku=${goodsId}&utm_source=union&utm_medium=api`;
    
    // 显示推广链接
    const promoModal = new bootstrap.Modal(document.getElementById('promoLinkModal'));
    document.getElementById('promoLinkTitle').textContent = '推广链接已生成';
    document.getElementById('promoLinkContent').innerHTML = `
        <div class="mb-3">
            <label class="form-label">商品名称：</label>
            <p>${goodsName || '未知商品'}</p>
        </div>
        <div class="mb-3">
            <label class="form-label">推广链接：</label>
            <div class="input-group">
                <input type="text" class="form-control" value="${promoLink}" id="promoLinkInput" readonly>
                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('promoLinkInput')">
                    <i class="bi bi-clipboard"></i> 复制
                </button>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">推广图片：</label>
            <div class="text-center border p-3">
                <img src="${goods.image}" class="img-fluid" style="max-height: 200px;" alt="${goodsName}">
                <div class="mt-2">
                    <a href="${promoLink}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-box-arrow-up-right"></i> 查看商品
                    </a>
                </div>
            </div>
        </div>
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> 提示：点击"复制"按钮可以复制推广链接，然后分享给您的用户。
        </div>
    `;
    
    // 关闭商品详情模态框（如果打开）
    const detailModal = bootstrap.Modal.getInstance(document.getElementById('goodsDetailModal'));
    if (detailModal) detailModal.hide();
    
    promoModal.show();
}

// 应用高级搜索
function applyAdvancedSearch() {
    const form = document.getElementById('advancedSearchForm');
    const keyword = form.keyword.value.trim().toLowerCase();
    const category = form.category.value;
    const minPrice = parseFloat(form.minPrice.value) || 0;
    const maxPrice = parseFloat(form.maxPrice.value) || Infinity;
    const inStockOnly = form.inStockOnly.checked;
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('searchModal'));
    if (modal) modal.hide();
    
    // 这里应该发送AJAX请求到后端进行搜索
    // 以下是模拟搜索逻辑
    currentGoodsList = currentGoodsList.filter(goods => {
        // 关键词匹配
        if (keyword && !goods.name.toLowerCase().includes(keyword) && !goods.id.toString().includes(keyword)) {
            return false;
        }
        
        // 类目筛选
        if (category && goods.category.toString() !== category) {
            return false;
        }
        
        // 价格区间
        const price = parseFloat(goods.price);
        if (price < minPrice || price > maxPrice) {
            return false;
        }
        
        // 库存状态
        if (inStockOnly && !goods.inStock) {
            return false;
        }
        
        return true;
    });
    
    // 排序
    const sortBy = form.sortBy.value;
    currentGoodsList.sort((a, b) => {
        switch (sortBy) {
            case 'price_asc':
                return parseFloat(a.price) - parseFloat(b.price);
            case 'price_desc':
                return parseFloat(b.price) - parseFloat(a.price);
            case 'sale_desc':
                return b.sales - a.sales;
            case 'commission_desc':
                return parseFloat(b.commissionRate) - parseFloat(a.commissionRate);
            default:
                return 0;
        }
    });
    
    totalGoods = currentGoodsList.length;
    currentPageNum = 1;
    renderGoodsList();
    renderPagination();
    
    // 显示搜索结果提示
    showAlert(`找到 ${totalGoods} 个符合条件的商品`, 'success');
}

// 同步商品
function syncGoods() {
    showLoading('syncGoodsBtn');
    
    // 模拟API请求
    setTimeout(() => {
        hideLoading('syncGoodsBtn');
        showAlert('商品同步完成', 'success');
        loadGoodsPage(); // 重新加载商品列表
    }, 2000);
}

// 导出商品
function exportGoods() {
    // 这里应该实现导出功能
    showAlert('导出功能开发中...', 'info');
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadGoodsPage();
    
    // 监听搜索框回车事件
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchGoods();
        }
    });
});
</script>

<!-- 推广链接模态框 -->
<div class="modal fade" id="promoLinkModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promoLinkTitle">推广链接</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="promoLinkContent">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
