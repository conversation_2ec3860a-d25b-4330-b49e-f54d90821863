<!-- 配置管理页面 -->
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h2>API配置管理</h2>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">京东联盟API配置</h5>
                </div>
                <div class="card-body">
                    <form id="apiConfigForm">
                        <div class="mb-3">
                            <label for="appKey" class="form-label">App Key</label>
                            <input type="text" class="form-control" id="appKey" name="appKey" required>
                        </div>
                        <div class="mb-3">
                            <label for="appSecret" class="form-label">App Secret</label>
                            <input type="password" class="form-control" id="appSecret" name="appSecret" required>
                        </div>
                        <div class="mb-3">
                            <label for="siteId" class="form-label">网站ID (siteId)</label>
                            <input type="text" class="form-control" id="siteId" name="siteId" required>
                        </div>
                        <div class="mb-3">
                            <label for="positionId" class="form-label">推广位ID (positionId)</label>
                            <input type="text" class="form-control" id="positionId" name="positionId">
                        </div>
                        <div class="mb-3">
                            <label for="unionId" class="form-label">联盟ID (unionId)</label>
                            <input type="text" class="form-control" id="unionId" name="unionId">
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="useOfficialFormat" name="useOfficialFormat" checked>
                            <label class="form-check-label" for="useOfficialFormat">使用官方格式 (use_official_format)</label>
                        </div>
                        <button type="submit" class="btn btn-primary">保存配置</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="testApi()">测试连接</button>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">系统配置</h5>
                </div>
                <div class="card-body">
                    <form id="systemConfigForm">
                        <div class="mb-3">
                            <label for="apiBaseUrl" class="form-label">API基础URL</label>
                            <input type="text" class="form-control" id="apiBaseUrl" name="apiBaseUrl" value="../">
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="autoRefresh" name="autoRefresh" checked>
                            <label class="form-check-label" for="autoRefresh">自动刷新数据</label>
                        </div>
                        <div class="mb-3">
                            <label for="refreshInterval" class="form-label">刷新间隔 (秒)</label>
                            <input type="number" class="form-control" id="refreshInterval" name="refreshInterval" value="30" min="10">
                        </div>
                        <button type="submit" class="btn btn-primary">保存设置</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">配置说明</h5>
                </div>
                <div class="card-body">
                    <h6>API配置说明：</h6>
                    <ul class="small">
                        <li><strong>App Key/Secret</strong>: 从京东联盟后台获取的API凭证</li>
                        <li><strong>网站ID (siteId)</strong>: 您的网站ID</li>
                        <li><strong>推广位ID (positionId)</strong>: 可选，用于生成推广链接</li>
                        <li><strong>联盟ID (unionId)</strong>: 可选，用于标识推广渠道</li>
                    </ul>
                    <hr>
                    <h6>注意事项：</h6>
                    <ul class="small text-muted">
                        <li>修改配置后需要点击"保存配置"按钮生效</li>
                        <li>建议先"测试连接"确保配置正确</li>
                        <li>API调用频率限制：1000次/分钟</li>
                        <li>请妥善保管您的API密钥</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载时获取配置
function loadConfigPage() {
    // 从localStorage加载配置
    const config = JSON.parse(localStorage.getItem('jdApiConfig') || '{}');
    
    // 填充表单
    if (config) {
        document.getElementById('appKey').value = config.appKey || '';
        document.getElementById('appSecret').value = config.appSecret || '';
        document.getElementById('siteId').value = config.siteId || '';
        document.getElementById('positionId').value = config.positionId || '';
        document.getElementById('unionId').value = config.unionId || '';
        document.getElementById('useOfficialFormat').checked = config.useOfficialFormat !== false;
    }
    
    // 加载系统配置
    const sysConfig = JSON.parse(localStorage.getItem('systemConfig') || '{}');
    if (sysConfig) {
        document.getElementById('apiBaseUrl').value = sysConfig.apiBaseUrl || '../';
        document.getElementById('autoRefresh').checked = sysConfig.autoRefresh !== false;
        document.getElementById('refreshInterval').value = sysConfig.refreshInterval || 30;
    }
    
    // 表单提交事件
    document.getElementById('apiConfigForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveApiConfig();
    });
    
    document.getElementById('systemConfigForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSystemConfig();
    });
}

// 保存API配置
function saveApiConfig() {
    const config = {
        appKey: document.getElementById('appKey').value,
        appSecret: document.getElementById('appSecret').value,
        siteId: document.getElementById('siteId').value,
        positionId: document.getElementById('positionId').value,
        unionId: document.getElementById('unionId').value,
        useOfficialFormat: document.getElementById('useOfficialFormat').checked
    };
    
    localStorage.setItem('jdApiConfig', JSON.stringify(config));
    showAlert('API配置已保存', 'success');
}

// 保存系统配置
function saveSystemConfig() {
    const config = {
        apiBaseUrl: document.getElementById('apiBaseUrl').value,
        autoRefresh: document.getElementById('autoRefresh').checked,
        refreshInterval: parseInt(document.getElementById('refreshInterval').value) || 30
    };
    
    localStorage.setItem('systemConfig', JSON.stringify(config));
    showAlert('系统配置已保存', 'success');
    
    // 更新全局配置
    if (window.apiBaseUrl) {
        window.apiBaseUrl = config.apiBaseUrl;
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadConfigPage();
});
</script>
