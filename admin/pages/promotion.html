<!-- 推广管理页面 -->
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h2>推广管理</h2>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#createPromoModal">
                <i class="bi bi-plus-circle"></i> 创建推广计划
            </button>
        </div>
    </div>

    <!-- 推广数据概览 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-4">
            <div class="card border-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">总点击量</h6>
                            <h3 class="mb-0">12,345</h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="bi bi-mouse text-primary" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="text-success"><i class="bi bi-arrow-up"></i> 12.5%</span>
                        <span class="text-muted">较上月</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card border-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">总订单数</h6>
                            <h3 class="mb-0">1,234</h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="bi bi-cart-check text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="text-success"><i class="bi bi-arrow-up"></i> 8.2%</span>
                        <span class="text-muted">较上月</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card border-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">总佣金</h6>
                            <h3 class="mb-0">¥12,345.67</h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="bi bi-cash-coin text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="text-success"><i class="bi bi-arrow-up"></i> 15.8%</span>
                        <span class="text-muted">较上月</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card border-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">转化率</h6>
                            <h3 class="mb-0">10.2%</h3>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="bi bi-graph-up-arrow text-info" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <span class="text-success"><i class="bi bi-arrow-up"></i> 2.1%</span>
                        <span class="text-muted">较上月</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 推广链接管理 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">推广链接列表</h5>
            <div class="input-group" style="width: 300px;">
                <input type="text" class="form-control" placeholder="搜索推广链接..." id="searchPromoInput">
                <button class="btn btn-outline-secondary" type="button" onclick="searchPromoLinks()">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>推广名称</th>
                            <th>链接类型</th>
                            <th>创建时间</th>
                            <th>点击量</th>
                            <th>订单数</th>
                            <th>佣金</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="promoLinksList">
                        <!-- 推广链接将通过JavaScript动态加载 -->
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center" id="promoPagination">
                    <!-- 分页将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 数据统计图表 -->
    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="statsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="daily-tab" data-bs-toggle="tab" data-bs-target="#dailyStats" type="button" role="tab" aria-controls="dailyStats" aria-selected="true">
                        每日统计
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="monthly-tab" data-bs-toggle="tab" data-bs-target="#monthlyStats" type="button" role="tab" aria-controls="monthlyStats" aria-selected="false">
                        月度统计
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="source-tab" data-bs-toggle="tab" data-bs-target="#sourceStats" type="button" role="tab" aria-controls="sourceStats" aria-selected="false">
                        来源分析
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="statsTabContent">
                <div class="tab-pane fade show active" id="dailyStats" role="tabpanel" aria-labelledby="daily-tab">
                    <div class="chart-container" style="position: relative; height: 300px;">
                        <canvas id="dailyChart"></canvas>
                    </div>
                </div>
                <div class="tab-pane fade" id="monthlyStats" role="tabpanel" aria-labelledby="monthly-tab">
                    <div class="chart-container" style="position: relative; height: 300px;">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
                <div class="tab-pane fade" id="sourceStats" role="tabpanel" aria-labelledby="source-tab">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container" style="position: relative; height: 300px;">
                                <canvas id="sourceChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>来源</th>
                                            <th>点击量</th>
                                            <th>占比</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sourceStatsTable">
                                        <!-- 来源统计数据将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建推广计划模态框 -->
<div class="modal fade" id="createPromoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建推广计划</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createPromoForm">
                    <ul class="nav nav-tabs mb-3" id="promoTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="single-tab" data-bs-toggle="tab" data-bs-target="#singlePromo" type="button" role="tab" aria-controls="singlePromo" aria-selected="true">
                                单品推广
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activityPromo" type="button" role="tab" aria-controls="activityPromo" aria-selected="false">
                                活动推广
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="custom-tab" data-bs-toggle="tab" data-bs-target="#customPromo" type="button" role="tab" aria-controls="customPromo" aria-selected="false">
                                自定义推广
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="promoTabContent">
                        <!-- 单品推广 -->
                        <div class="tab-pane fade show active" id="singlePromo" role="tabpanel" aria-labelledby="single-tab">
                            <div class="mb-3">
                                <label for="goodsUrl" class="form-label">商品链接</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="goodsUrl" placeholder="输入京东商品链接或SKU">
                                    <button class="btn btn-outline-secondary" type="button" onclick="searchGoodsByUrl()">查询</button>
                                </div>
                            </div>
                            
                            <div id="goodsInfo" class="d-none">
                                <div class="card mb-3">
                                    <div class="row g-0">
                                        <div class="col-md-3">
                                            <img id="goodsImage" src="" class="img-fluid rounded-start" alt="商品图片">
                                        </div>
                                        <div class="col-md-9">
                                            <div class="card-body">
                                                <h5 class="card-title" id="goodsTitle">商品标题</h5>
                                                <p class="card-text">
                                                    <span class="text-danger fw-bold" id="goodsPrice">¥0.00</span>
                                                    <span class="text-muted ms-2">佣金: <span id="goodsCommission">¥0.00</span> (<span id="goodsCommissionRate">0</span>%)</span>
                                                </p>
                                                <p class="card-text"><small class="text-muted" id="goodsSku">SKU: -</small></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="promoName" class="form-label">推广名称</label>
                                    <input type="text" class="form-control" id="promoName" placeholder="例如：夏季爆款T恤限时特惠">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="promoChannel" class="form-label">推广渠道</label>
                                    <select class="form-select" id="promoChannel">
                                        <option value="wechat">微信</option>
                                        <option value="weibo">微博</option>
                                        <option value="qq">QQ</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">推广链接</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="promoLink" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('promoLink')">
                                            <i class="bi bi-clipboard"></i> 复制
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="promoNote" class="form-label">推广备注</label>
                                    <textarea class="form-control" id="promoNote" rows="2" placeholder="可填写推广计划、目标人群等信息（选填）"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 活动推广 -->
                        <div class="tab-pane fade" id="activityPromo" role="tabpanel" aria-labelledby="activity-tab">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> 从京东联盟活动列表中选择活动进行推广
                            </div>
                            
                            <div class="mb-3">
                                <label for="activityType" class="form-label">活动类型</label>
                                <select class="form-select" id="activityType">
                                    <option value="">全部活动</option>
                                    <option value="1">限时秒杀</option>
                                    <option value="2">品牌特卖</option>
                                    <option value="3">新品尝鲜</option>
                                    <option value="4">领券中心</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">选择活动</label>
                                <div class="list-group" style="max-height: 300px; overflow-y: auto;">
                                    <!-- 活动列表将通过JavaScript动态加载 -->
                                    <div class="list-group-item">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">618年中大促</h6>
                                            <small>2023-06-01 至 2023-06-20</small>
                                        </div>
                                        <p class="mb-1 small text-muted">全品类满300减40，上不封顶</p>
                                        <button class="btn btn-sm btn-outline-primary mt-2">选择</button>
                                    </div>
                                    <div class="list-group-item">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">京东家电超级品类日</h6>
                                            <small>2023-06-15 至 2023-06-17</small>
                                        </div>
                                        <p class="mb-1 small text-muted">家电5折起，以旧换新再补贴</p>
                                        <button class="btn btn-sm btn-outline-primary mt-2">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 自定义推广 -->
                        <div class="tab-pane fade" id="customPromo" role="tabpanel" aria-labelledby="custom-tab">
                            <div class="mb-3">
                                <label for="customUrl" class="form-label">推广链接</label>
                                <input type="url" class="form-control" id="customUrl" placeholder="输入要推广的京东链接">
                                <div class="form-text">支持京东商品、活动、店铺等链接</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="customName" class="form-label">推广名称</label>
                                <input type="text" class="form-control" id="customName" placeholder="例如：618大促主会场">
                            </div>
                            
                            <div class="mb-3">
                                <label for="customChannel" class="form-label">推广渠道</label>
                                <select class="form-select" id="customChannel">
                                    <option value="wechat">微信</option>
                                    <option value="weibo">微博</option>
                                    <option value="qq">QQ</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="customNote" class="form-label">推广备注</label>
                                <textarea class="form-control" id="customNote" rows="3" placeholder="可填写推广计划、目标人群等信息（选填）"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createPromotion()">创建推广</button>
            </div>
        </div>
    </div>
</div>

<script>
// 推广链接数据
let promoLinks = [];
let currentPromoPage = 1;
const promoPageSize = 10;

// 页面加载时初始化
function loadPromotionPage() {
    // 模拟加载推广链接数据
    loadPromoLinks();
    
    // 初始化图表
    initCharts();
    
    // 绑定事件
    document.getElementById('searchPromoInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchPromoLinks();
        }
    });
}

// 加载推广链接
function loadPromoLinks() {
    // 显示加载中
    document.getElementById('promoLinksList').innerHTML = `
        <tr>
            <td colspan="8" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </td>
        </tr>
    `;
    
    // 模拟API请求
    setTimeout(() => {
        // 模拟数据
        promoLinks = [];
        const types = ['单品', '活动', '店铺', '会场'];
        const statuses = ['进行中', '已暂停', '已结束'];
        const statusClasses = ['success', 'warning', 'secondary'];
        
        for (let i = 1; i <= 25; i++) {
            const typeIndex = Math.floor(Math.random() * types.length);
            const statusIndex = Math.floor(Math.random() * statuses.length);
            const clicks = Math.floor(Math.random() * 10000);
            const orders = Math.floor(clicks * (Math.random() * 0.1 + 0.01));
            const commission = (orders * (Math.random() * 50 + 5)).toFixed(2);
            
            promoLinks.push({
                id: 'P' + (10000 + i),
                name: `推广活动 ${i} - ${types[typeIndex]}`,
                type: types[typeIndex],
                createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                clicks: clicks,
                orders: orders,
                commission: commission,
                status: statuses[statusIndex],
                statusClass: statusClasses[statusIndex]
            });
        }
        
        renderPromoLinks();
        renderPromoPagination();
    }, 800);
}

// 渲染推广链接列表
function renderPromoLinks() {
    const start = (currentPromoPage - 1) * promoPageSize;
    const end = start + promoPageSize;
    const linksToShow = promoLinks.slice(start, end);
    
    if (linksToShow.length === 0) {
        document.getElementById('promoLinksList').innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-5 text-muted">
                    <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                    <div class="mt-2">没有找到推广链接</div>
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    linksToShow.forEach(link => {
        html += `
            <tr>
                <td>
                    <div class="fw-bold">${link.name}</div>
                    <small class="text-muted">ID: ${link.id}</small>
                </td>
                <td>${link.type}</td>
                <td>${link.createTime}</td>
                <td>${link.clicks.toLocaleString()}</td>
                <td>${link.orders.toLocaleString()}</td>
                <td class="text-success fw-bold">¥${parseFloat(link.commission).toLocaleString('zh-CN', {minimumFractionDigits: 2})}</td>
                <td><span class="badge bg-${link.statusClass}">${link.status}</span></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewPromoDetail('${link.id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="copyPromoLink('${link.id}')">
                            <i class="bi bi-link-45deg"></i>
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-three-dots-vertical"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#" onclick="editPromo('${link.id}')"><i class="bi bi-pencil"></i> 编辑</a></li>
                                <li><a class="dropdown-item" href="#" onclick="togglePromoStatus('${link.id}')">
                                    <i class="bi ${link.status === '已暂停' ? 'bi-play' : 'bi-pause'}"></i> ${link.status === '已暂停' ? '启用' : '暂停'}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deletePromo('${link.id}')"><i class="bi bi-trash"></i> 删除</a></li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    });
    
    document.getElementById('promoLinksList').innerHTML = html;
}

// 渲染推广链接分页
function renderPromoPagination() {
    const totalPages = Math.ceil(promoLinks.length / promoPageSize);
    let html = '';
    
    // 上一页按钮
    html += `
        <li class="page-item ${currentPromoPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePromoPage(${currentPromoPage - 1})" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPromoPage - 2 && i <= currentPromoPage + 2)) {
            html += `
                <li class="page-item ${i === currentPromoPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePromoPage(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPromoPage - 3 || i === currentPromoPage + 3) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // 下一页按钮
    html += `
        <li class="page-item ${currentPromoPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePromoPage(${currentPromoPage + 1})" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;
    
    document.getElementById('promoPagination').innerHTML = html;
}

// 切换推广链接页码
function changePromoPage(pageNum) {
    if (pageNum < 1 || pageNum > Math.ceil(promoLinks.length / promoPageSize)) return;
    currentPromoPage = pageNum;
    renderPromoLinks();
    window.scrollTo(0, 0);
}

// 搜索推广链接
function searchPromoLinks() {
    const keyword = document.getElementById('searchPromoInput').value.trim().toLowerCase();
    if (!keyword) {
        renderPromoLinks();
        return;
    }
    
    const filteredLinks = promoLinks.filter(link => 
        link.name.toLowerCase().includes(keyword) || 
        link.id.toLowerCase().includes(keyword) ||
        link.type.toLowerCase().includes(keyword)
    );
    
    promoLinks = [...filteredLinks];
    currentPromoPage = 1;
    renderPromoLinks();
    renderPromoPagination();
}

// 查看推广链接详情
function viewPromoDetail(promoId) {
    const promo = promoLinks.find(p => p.id === promoId);
    if (!promo) return;
    
    // 这里应该打开一个模态框显示推广详情
    // 由于模态框内容较多，这里简化为显示一个提示
    showAlert(`查看推广详情: ${promo.name}`, 'info');
}

// 复制推广链接
function copyPromoLink(promoId) {
    const promo = promoLinks.find(p => p.id === promoId);
    if (!promo) return;
    
    // 模拟生成推广链接
    const promoLink = `https://u.jd.com/abc123?promo=${promoId}&utm_source=union&utm_medium=admin`;
    
    // 复制到剪贴板
    navigator.clipboard.writeText(promoLink).then(() => {
        showAlert('推广链接已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        showAlert('复制失败，请手动复制', 'danger');
    });
}

// 创建推广
function createPromotion() {
    // 这里应该实现创建推广的逻辑
    // 由于需要与后端API交互，这里简化为显示一个提示
    showAlert('推广创建成功', 'success');
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('createPromoModal'));
    if (modal) modal.hide();
    
    // 刷新推广链接列表
    loadPromoLinks();
}

// 初始化图表
function initCharts() {
    // 这里应该使用Chart.js等库初始化图表
    // 由于是前端模拟，这里只显示一个占位符
    console.log('初始化图表...');
    
    // 模拟图表数据
    const dailyCtx = document.getElementById('dailyChart');
    const monthlyCtx = document.getElementById('monthlyChart');
    const sourceCtx = document.getElementById('sourceChart');
    
    // 实际项目中应该使用Chart.js等库渲染图表
    // 这里只显示一个简单的文本提示
    if (dailyCtx) {
        dailyCtx.innerHTML = '<div class="text-center text-muted py-5">每日点击量和订单量趋势图</div>';
    }
    
    if (monthlyCtx) {
        monthlyCtx.innerHTML = '<div class="text-center text-muted py-5">月度佣金和转化率趋势图</div>';
    }
    
    if (sourceCtx) {
        sourceCtx.innerHTML = '<div class="text-center text-muted py-5">流量来源分布图</div>';
    }
    
    // 填充来源统计表格
    const sources = [
        { name: '微信', clicks: 3560, percent: 42.5 },
        { name: 'QQ', clicks: 1890, percent: 22.6 },
        { name: '微博', clicks: 1250, percent: 14.9 },
        { name: '直接访问', clicks: 980, percent: 11.7 },
        { name: '其他', clicks: 700, percent: 8.3 }
    ];
    
    let sourceTableHtml = '';
    sources.forEach(source => {
        sourceTableHtml += `
            <tr>
                <td>${source.name}</td>
                <td>${source.clicks.toLocaleString()}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="progress flex-grow-1 me-2" style="height: 6px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: ${source.percent}%" 
                                 aria-valuenow="${source.percent}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span>${source.percent}%</span>
                    </div>
                </td>
            </tr>
        `;
    });
    
    document.getElementById('sourceStatsTable').innerHTML = sourceTableHtml;
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadPromotionPage();
});
</script>
