<!-- 日志查看页面 -->
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h2>系统日志</h2>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportLogs()">
                    <i class="bi bi-download"></i> 导出日志
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="showClearLogsConfirm()">
                    <i class="bi bi-trash"></i> 清空日志
                </button>
            </div>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="logLevelDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-filter"></i> 日志级别
                </button>
                <ul class="dropdown-menu" aria-labelledby="logLevelDropdown">
                    <li><a class="dropdown-item active" href="#" onclick="filterLogs('all')">全部</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="filterLogs('info')"><span class="badge bg-info me-1">INFO</span> 信息</a></li>
                    <li><a class="dropdown-item" href="#" onclick="filterLogs('success')"><span class="badge bg-success me-1">SUCCESS</span> 成功</a></li>
                    <li><a class="dropdown-item" href="#" onclick="filterLogs('warning')"><span class="badge bg-warning me-1">WARNING</span> 警告</a></li>
                    <li><a class="dropdown-item" href="#" onclick="filterLogs('error')"><span class="badge bg-danger me-1">ERROR</span> 错误</a></li>
                    <li><a class="dropdown-item" href="#" onclick="filterLogs('debug')"><span class="badge bg-secondary me-1">DEBUG</span> 调试</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 日志筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="logFilterForm" onsubmit="return false;">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="logDateRange" class="form-label">日期范围</label>
                        <div class="input-group">
                            <input type="date" class="form-control" id="startDate">
                            <span class="input-group-text">至</span>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="logModule" class="form-label">模块</label>
                        <select class="form-select" id="logModule">
                            <option value="">全部模块</option>
                            <option value="api">API接口</option>
                            <option value="auth">认证授权</option>
                            <option value="goods">商品管理</option>
                            <option value="promotion">推广管理</option>
                            <option value="system">系统管理</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="logKeyword" class="form-label">关键词搜索</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="logKeyword" placeholder="输入关键词搜索日志内容...">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchLogs()">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-secondary w-100" onclick="resetLogFilters()">
                            <i class="bi bi-arrow-counterclockwise"></i> 重置
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 日志列表 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">日志记录</h5>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="autoRefreshLogs" checked>
                <label class="form-check-label" for="autoRefreshLogs">自动刷新</label>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="120">时间</th>
                            <th width="100">级别</th>
                            <th width="120">模块</th>
                            <th>内容</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody id="logList">
                        <!-- 日志列表将通过JavaScript动态加载 -->
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2">正在加载日志数据...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-transparent">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    共 <span id="totalLogs">0</span> 条记录，显示 <span id="showingLogs">0</span> 条
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0" id="logPagination">
                        <!-- 分页将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">日志详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="logDetailContent">
                <!-- 日志详情内容将通过JavaScript动态加载 -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="copyLogDetail()">
                    <i class="bi bi-clipboard"></i> 复制
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 清空日志确认对话框 -->
<div class="modal fade" id="clearLogsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>确认清空日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要清空所有日志记录吗？此操作不可恢复！</p>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="confirmBackup">
                    <label class="form-check-label" for="confirmBackup">
                        我已备份重要日志
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmClearLogsBtn" disabled>
                    <i class="bi bi-trash"></i> 确认清空
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 日志数据
let logs = [];
let filteredLogs = [];
let currentLogPage = 1;
const logsPerPage = 20;
let autoRefreshInterval = null;
let currentFilter = 'all';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadLogsPage();
    
    // 设置确认清空日志按钮点击事件
    document.getElementById('confirmClearLogsBtn').addEventListener('click', clearLogs);
    
    // 自动刷新开关事件
    document.getElementById('autoRefreshLogs').addEventListener('change', function() {
        if (this.checked) {
            setupAutoRefresh();
        } else {
            clearAutoRefresh();
        }
    });
    
    // 确认备份复选框事件
    document.getElementById('confirmBackup').addEventListener('change', function() {
        document.getElementById('confirmClearLogsBtn').disabled = !this.checked;
    });
    
    // 搜索框回车事件
    document.getElementById('logKeyword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchLogs();
        }
    });
});

// 加载日志页面
function loadLogsPage() {
    // 设置默认日期范围（最近7天）
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    
    document.getElementById('startDate').valueAsDate = startDate;
    document.getElementById('endDate').valueAsDate = endDate;
    
    // 加载日志数据
    loadLogs();
    
    // 设置自动刷新
    setupAutoRefresh();
}

// 加载日志数据
function loadLogs() {
    // 显示加载中
    document.getElementById('logList').innerHTML = `
        <tr>
            <td colspan="5" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载日志数据...</div>
            </td>
        </tr>
    `;
    
    // 模拟API请求
    setTimeout(() => {
        // 生成模拟日志数据
        logs = [];
        const modules = ['api', 'auth', 'goods', 'promotion', 'system'];
        const levels = [
            { type: 'info', label: 'INFO', class: 'info' },
            { type: 'success', label: 'SUCCESS', class: 'success' },
            { type: 'warning', label: 'WARNING', class: 'warning' },
            { type: 'error', label: 'ERROR', class: 'danger' },
            { type: 'debug', label: 'DEBUG', class: 'secondary' }
        ];
        const actions = [
            '用户登录', 'API调用', '商品同步', '订单处理', '缓存更新',
            '权限验证', '数据备份', '系统启动', '定时任务', '配置更新'
        ];
        const ips = ['192.168.1.', '10.0.0.', '172.16.0.'];
        
        // 生成30天内的随机日志
        for (let i = 0; i < 150; i++) {
            const date = new Date();
            date.setDate(date.getDate() - Math.floor(Math.random() * 30));
            date.setHours(Math.floor(Math.random() * 24));
            date.setMinutes(Math.floor(Math.random() * 60));
            date.setSeconds(Math.floor(Math.random() * 60));
            
            const level = levels[Math.floor(Math.random() * levels.length)];
            const module = modules[Math.floor(Math.random() * modules.length)];
            const action = actions[Math.floor(Math.random() * actions.length)];
            const ip = ips[Math.floor(Math.random() * ips.length)] + Math.floor(Math.random() * 255);
            const userId = 'user' + (1000 + Math.floor(Math.random() * 1000));
            
            logs.push({
                id: 'log-' + Date.now() + '-' + i,
                timestamp: date.getTime(),
                time: date.toISOString().replace('T', ' ').substr(0, 19),
                level: level,
                module: module,
                ip: ip,
                userId: userId,
                action: action,
                message: `${action}${['成功', '失败', '完成', '异常', '超时'][Math.floor(Math.random() * 5)]}`,
                details: {
                    requestId: 'req-' + Math.random().toString(36).substr(2, 8),
                    method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
                    path: `/api/${module}/${['list', 'detail', 'create', 'update', 'delete'][Math.floor(Math.random() * 5)]}`,
                    params: { id: Math.floor(Math.random() * 1000) + 1000 },
                    response: {
                        code: [200, 200, 200, 200, 400, 401, 403, 404, 500][Math.floor(Math.random() * 9)],
                        message: '操作成功'
                    },
                    stack: level.type === 'error' ? 'Error: Something went wrong\n    at Object.<anonymous> (/path/to/file.js:10:15)\n    at Module._compile (internal/modules/cjs/loader.js:1063:30)' : null
                }
            });
        }
        
        // 按时间倒序排序
        logs.sort((a, b) => b.timestamp - a.timestamp);
        filteredLogs = [...logs];
        
        // 渲染日志列表
        renderLogs();
        updateLogsCount();
    }, 800);
}

// 渲染日志列表
function renderLogs() {
    const start = (currentLogPage - 1) * logsPerPage;
    const end = start + logsPerPage;
    const logsToShow = filteredLogs.slice(start, end);
    
    if (logsToShow.length === 0) {
        document.getElementById('logList').innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-5 text-muted">
                    <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                    <div class="mt-2">没有找到日志记录</div>
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    logsToShow.forEach(log => {
        const time = new Date(log.timestamp);
        const timeStr = time.toLocaleTimeString('zh-CN', { hour12: false });
        const dateStr = time.toLocaleDateString('zh-CN').replace(/\//g, '-');
        
        // 截取过长的消息
        let shortMessage = log.message;
        if (shortMessage.length > 80) {
            shortMessage = shortMessage.substring(0, 80) + '...';
        }
        
        html += `
            <tr class="log-row" data-log-id="${log.id}" data-level="${log.level.type}">
                <td>
                    <div>${dateStr}</div>
                    <small class="text-muted">${timeStr}</small>
                </td>
                <td>
                    <span class="badge bg-${log.level.class}">${log.level.label}</span>
                </td>
                <td>
                    <span class="badge bg-light text-dark">${log.module}</span>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 400px;" title="${log.message}">
                        ${shortMessage}
                    </div>
                    <div class="small text-muted">
                        <span>${log.ip}</span>
                        ${log.userId ? `<span class="ms-2">${log.userId}</span>` : ''}
                    </div>
                </td>
                <td class="text-end">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetail('${log.id}')">
                        <i class="bi bi-search"></i> 详情
                    </button>
                </td>
            </tr>
        `;
    });
    
    document.getElementById('logList').innerHTML = html;
    renderLogPagination();
    updateLogsCount();
}

// 渲染日志分页
function renderLogPagination() {
    const totalPages = Math.ceil(filteredLogs.length / logsPerPage);
    let html = '';
    
    // 上一页按钮
    html += `
        <li class="page-item ${currentLogPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changeLogPage(${currentLogPage - 1})" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentLogPage - 2 && i <= currentLogPage + 2)) {
            html += `
                <li class="page-item ${i === currentLogPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changeLogPage(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentLogPage - 3 || i === currentLogPage + 3) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // 下一页按钮
    html += `
        <li class="page-item ${currentLogPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changeLogPage(${currentLogPage + 1})" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;
    
    document.getElementById('logPagination').innerHTML = html;
}

// 更新日志计数
function updateLogsCount() {
    const total = filteredLogs.length;
    const start = (currentLogPage - 1) * logsPerPage + 1;
    const end = Math.min(start + logsPerPage - 1, total);
    
    document.getElementById('totalLogs').textContent = total.toLocaleString();
    document.getElementById('showingLogs').textContent = total > 0 ? `${start}-${end}` : '0';
}

// 切换日志页码
function changeLogPage(pageNum) {
    if (pageNum < 1 || pageNum > Math.ceil(filteredLogs.length / logsPerPage)) return;
    currentLogPage = pageNum;
    renderLogs();
    window.scrollTo(0, 0);
}

// 查看日志详情
function viewLogDetail(logId) {
    const log = logs.find(l => l.id === logId);
    if (!log) return;
    
    const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
    
    // 构建日志详情HTML
    let detailsHtml = `
        <div class="mb-3">
            <h6>基本信息</h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <tr>
                        <th width="120">时间</th>
                        <td>${log.time}</td>
                    </tr>
                    <tr>
                        <th>级别</th>
                        <td><span class="badge bg-${log.level.class}">${log.level.label}</span></td>
                    </tr>
                    <tr>
                        <th>模块</th>
                        <td>${log.module}</td>
                    </tr>
                    <tr>
                        <th>IP</th>
                        <td>${log.ip}</td>
                    </tr>
                    <tr>
                        <th>用户</th>
                        <td>${log.userId || '-'}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="mb-3">
            <h6>消息内容</h6>
            <div class="p-3 bg-light rounded">
                <pre class="mb-0">${escapeHtml(log.message)}</pre>
            </div>
        </div>
    `;
    
    // 添加请求详情
    if (log.details) {
        detailsHtml += `
            <div class="mb-3">
                <h6>请求详情</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <tr>
                            <th width="120">请求ID</th>
                            <td>${log.details.requestId || '-'}</td>
                        </tr>
                        <tr>
                            <th>方法</th>
                            <td><span class="badge bg-secondary">${log.details.method || 'GET'}</span></td>
                        </tr>
                        <tr>
                            <th>路径</th>
                            <td><code>${log.details.path || '-'}</code></td>
                        </tr>
                        <tr>
                            <th>参数</th>
                            <td><pre class="mb-0">${JSON.stringify(log.details.params || {}, null, 2)}</pre></td>
                        </tr>
                        <tr>
                            <th>响应</th>
                            <td>
                                <div>状态码: <span class="badge bg-${log.details.response && log.details.response.code >= 400 ? 'danger' : 'success'}">${log.details.response ? log.details.response.code : '-'}</span></div>
                                <div class="mt-1">消息: ${log.details.response ? log.details.response.message : '-'}</div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        `;
    }
    
    // 添加堆栈跟踪（如果是错误）
    if (log.details && log.details.stack) {
        detailsHtml += `
            <div class="mb-3">
                <h6>堆栈跟踪</h6>
                <div class="p-3 bg-dark text-light rounded">
                    <pre class="mb-0 text-white-50">${escapeHtml(log.details.stack)}</pre>
                </div>
            </div>
        `;
    }
    
    document.getElementById('logDetailContent').innerHTML = detailsHtml;
    modal.show();
}

// 复制日志详情
function copyLogDetail() {
    const detailContent = document.getElementById('logDetailContent').textContent;
    navigator.clipboard.writeText(detailContent).then(() => {
        showAlert('日志详情已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        showAlert('复制失败，请手动复制', 'danger');
    });
}

// 搜索日志
function searchLogs() {
    const keyword = document.getElementById('logKeyword').value.trim().toLowerCase();
    const module = document.getElementById('logModule').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    filteredLogs = logs.filter(log => {
        // 关键词搜索
        if (keyword && !(
            log.message.toLowerCase().includes(keyword) ||
            log.module.toLowerCase().includes(keyword) ||
            log.ip.includes(keyword) ||
            (log.userId && log.userId.toLowerCase().includes(keyword)) ||
            (log.details && JSON.stringify(log.details).toLowerCase().includes(keyword))
        )) {
            return false;
        }
        
        // 模块筛选
        if (module && log.module !== module) {
            return false;
        }
        
        // 日期范围筛选
        const logDate = new Date(log.timestamp).toISOString().split('T')[0];
        if (startDate && logDate < startDate) {
            return false;
        }
        if (endDate && logDate > endDate) {
            return false;
        }
        
        // 日志级别筛选
        if (currentFilter !== 'all' && log.level.type !== currentFilter) {
            return false;
        }
        
        return true;
    });
    
    currentLogPage = 1;
    renderLogs();
}

// 重置筛选条件
function resetLogFilters() {
    document.getElementById('logFilterForm').reset();
    document.getElementById('startDate').valueAsDate = new Date(new Date().setDate(new Date().getDate() - 7));
    document.getElementById('endDate').valueAsDate = new Date();
    currentFilter = 'all';
    document.querySelectorAll('#logLevelDropdown .dropdown-item').forEach(item => {
        item.classList.toggle('active', item.getAttribute('onclick').includes("'all'"));
    });
    
    filteredLogs = [...logs];
    currentLogPage = 1;
    renderLogs();
}

// 按日志级别筛选
function filterLogs(level) {
    currentFilter = level;
    document.querySelectorAll('#logLevelDropdown .dropdown-item').forEach(item => {
        item.classList.toggle('active', item.getAttribute('onclick').includes(`'${level}'`));
    });
    
    if (level === 'all') {
        filteredLogs = [...logs];
    } else {
        filteredLogs = logs.filter(log => log.level.type === level);
    }
    
    currentLogPage = 1;
    renderLogs();
}

// 显示清空日志确认对话框
function showClearLogsConfirm() {
    const modal = new bootstrap.Modal(document.getElementById('clearLogsModal'));
    modal.show();
}

// 清空日志
function clearLogs() {
    // 这里应该发送请求到后端清空日志
    // 模拟清空日志
    logs = [];
    filteredLogs = [];
    currentLogPage = 1;
    
    // 关闭确认对话框
    const modal = bootstrap.Modal.getInstance(document.getElementById('clearLogsModal'));
    if (modal) modal.hide();
    
    // 重置确认复选框
    document.getElementById('confirmBackup').checked = false;
    document.getElementById('confirmClearLogsBtn').disabled = true;
    
    // 重新渲染日志列表
    renderLogs();
    
    showAlert('日志已清空', 'success');
}

// 导出日志
function exportLogs() {
    // 这里应该实现导出功能
    showAlert('日志导出功能开发中...', 'info');
}

// 设置自动刷新
function setupAutoRefresh() {
    clearAutoRefresh();
    autoRefreshInterval = setInterval(() => {
        if (document.getElementById('autoRefreshLogs')?.checked) {
            loadLogs();
        }
    }, 30000); // 30秒刷新一次
}

// 清除自动刷新
function clearAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// 转义HTML特殊字符
function escapeHtml(unsafe) {
    if (!unsafe) return '';
    return unsafe
        .toString()
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // 创建一个临时元素来存放alert
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = alertHtml;
    
    // 显示在页面顶部
    document.body.insertBefore(tempDiv.firstElementChild, document.body.firstChild);
    
    // 5秒后自动消失
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}
</script>